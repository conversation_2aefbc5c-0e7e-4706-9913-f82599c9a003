"""
测试线程池并行发送功能
"""
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

def simulate_station_data_generation(station_id, loop_num):
    """模拟车站数据生成"""
    # 模拟数据生成耗时
    time.sleep(0.1)  # 100ms
    
    messages = []
    for device_num in range(1, 4):  # 模拟3个设备
        message = {
            "topic": f"L03/{station_id}/PSD/SX/ASD{device_num:02d}",
            "payload": {
                "timestamp": int(time.time() * 1000),
                "loop": loop_num,
                "HDM_YGDW": (loop_num + device_num) % 2,
                "station_id": station_id
            }
        }
        messages.append(message)
    
    return messages

def simulate_mqtt_publish(topic, payload):
    """模拟MQTT发送"""
    # 模拟网络发送耗时
    time.sleep(0.01)  # 10ms
    return True

def send_station_data_serial(station_configs, loop_num):
    """串行发送车站数据（原始方式）"""
    start_time = time.time()
    total_messages = 0
    
    print(f"串行发送 - 循环 {loop_num} 开始")
    
    for station_config in station_configs:
        station_id = station_config["station_id"]
        
        # 生成数据
        messages = simulate_station_data_generation(station_id, loop_num)
        
        # 发送数据
        for msg in messages:
            simulate_mqtt_publish(msg["topic"], json.dumps(msg["payload"]))
            total_messages += 1
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print(f"串行发送 - 循环 {loop_num} 完成，发送 {total_messages} 条消息，耗时 {elapsed_time:.2f} 秒")
    return total_messages, elapsed_time

def send_single_station(station_config, loop_num):
    """发送单个车站的数据"""
    station_id = station_config["station_id"]
    
    # 生成数据
    messages = simulate_station_data_generation(station_id, loop_num)
    
    # 发送数据
    messages_sent = 0
    for msg in messages:
        simulate_mqtt_publish(msg["topic"], json.dumps(msg["payload"]))
        messages_sent += 1
    
    return messages_sent

def send_station_data_parallel(station_configs, loop_num, max_workers=5):
    """并行发送车站数据（线程池方式）"""
    start_time = time.time()
    total_messages = 0
    
    print(f"并行发送 - 循环 {loop_num} 开始，最大并发数: {max_workers}")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有车站的发送任务
        future_to_station = {}
        for station_config in station_configs:
            future = executor.submit(send_single_station, station_config, loop_num)
            future_to_station[future] = station_config["station_id"]
        
        # 收集结果
        for future in as_completed(future_to_station):
            station_id = future_to_station[future]
            try:
                messages_sent = future.result(timeout=30)
                total_messages += messages_sent
                print(f"  车站 {station_id} 完成，发送 {messages_sent} 条消息")
            except Exception as e:
                print(f"  车站 {station_id} 发送失败: {e}")
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print(f"并行发送 - 循环 {loop_num} 完成，发送 {total_messages} 条消息，耗时 {elapsed_time:.2f} 秒")
    return total_messages, elapsed_time

def test_performance_comparison():
    """测试性能对比"""
    print("=== 线程池并行发送性能测试 ===\n")
    
    # 创建测试车站配置
    station_configs = []
    for i in range(1, 11):  # 10个车站
        station_configs.append({
            "station_id": f"S{i:02d}",
            "station_name": f"测试车站{i}",
            "enabled": True
        })
    
    print(f"测试配置: {len(station_configs)} 个车站，每个车站3个设备")
    print(f"预计总消息数: {len(station_configs) * 3} 条/循环")
    print()
    
    # 测试串行发送
    print("1. 串行发送测试:")
    serial_times = []
    for loop in range(1, 4):
        _, elapsed = send_station_data_serial(station_configs, loop)
        serial_times.append(elapsed)
    
    avg_serial_time = sum(serial_times) / len(serial_times)
    print(f"串行发送平均耗时: {avg_serial_time:.2f} 秒")
    print()
    
    # 测试并行发送（不同并发数）
    print("2. 并行发送测试:")
    parallel_results = {}
    
    for max_workers in [2, 5, 10]:
        print(f"\n并发数 = {max_workers}:")
        parallel_times = []
        
        for loop in range(1, 4):
            _, elapsed = send_station_data_parallel(station_configs, loop, max_workers)
            parallel_times.append(elapsed)
        
        avg_parallel_time = sum(parallel_times) / len(parallel_times)
        speedup = avg_serial_time / avg_parallel_time
        parallel_results[max_workers] = {
            'avg_time': avg_parallel_time,
            'speedup': speedup
        }
        
        print(f"并发数 {max_workers} 平均耗时: {avg_parallel_time:.2f} 秒")
        print(f"性能提升: {speedup:.2f}x")
    
    # 性能总结
    print("\n=== 性能总结 ===")
    print(f"串行发送: {avg_serial_time:.2f} 秒")
    for workers, result in parallel_results.items():
        print(f"并发数 {workers}: {result['avg_time']:.2f} 秒 (提升 {result['speedup']:.2f}x)")
    
    # 推荐配置
    best_workers = max(parallel_results.keys(), key=lambda k: parallel_results[k]['speedup'])
    print(f"\n推荐并发数: {best_workers} (性能提升 {parallel_results[best_workers]['speedup']:.2f}x)")

def test_thread_safety():
    """测试线程安全性"""
    print("\n=== 线程安全性测试 ===\n")
    
    shared_counter = {"value": 0}
    lock = threading.Lock()
    
    def increment_counter(worker_id, iterations):
        for i in range(iterations):
            with lock:
                shared_counter["value"] += 1
        print(f"工作线程 {worker_id} 完成 {iterations} 次递增")
    
    # 使用线程池测试
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = []
        for worker_id in range(5):
            future = executor.submit(increment_counter, worker_id, 100)
            futures.append(future)
        
        # 等待所有任务完成
        for future in as_completed(futures):
            future.result()
    
    expected_value = 5 * 100
    actual_value = shared_counter["value"]
    
    print(f"预期值: {expected_value}")
    print(f"实际值: {actual_value}")
    
    if actual_value == expected_value:
        print("✅ 线程安全性测试通过")
    else:
        print("❌ 线程安全性测试失败")

def test_error_handling():
    """测试错误处理"""
    print("\n=== 错误处理测试 ===\n")
    
    def failing_task(task_id):
        if task_id == 2:
            raise Exception(f"任务 {task_id} 故意失败")
        time.sleep(0.1)
        return f"任务 {task_id} 成功"
    
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for task_id in range(5):
            future = executor.submit(failing_task, task_id)
            futures.append((future, task_id))
        
        success_count = 0
        error_count = 0
        
        for future, task_id in futures:
            try:
                result = future.result(timeout=5)
                print(f"✅ {result}")
                success_count += 1
            except Exception as e:
                print(f"❌ 任务 {task_id} 失败: {e}")
                error_count += 1
    
    print(f"\n成功: {success_count}, 失败: {error_count}")
    print("✅ 错误处理测试完成")

if __name__ == "__main__":
    test_performance_comparison()
    test_thread_safety()
    test_error_handling()
    
    print("\n=== 使用建议 ===")
    print("1. 根据车站数量和网络条件调整并发数")
    print("2. 建议并发数设置为 min(车站数量, CPU核心数 * 2)")
    print("3. 监控内存和网络使用情况")
    print("4. 对于大量车站，可以考虑分批处理")
