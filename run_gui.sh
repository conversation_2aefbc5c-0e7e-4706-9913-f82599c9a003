#!/bin/bash
# Simple script to run the GUI application

echo "=== Starting Multi-Station MQTT Data Generator ==="
echo "Attempting to run with available Python..."

# Try Python 3 first
if command -v python3 &> /dev/null; then
    echo "Trying Python 3..."
    python3 multi_station_gui.py
elif command -v python &> /dev/null; then
    echo "Trying Python 2.7..."
    python multi_station_gui.py
else
    echo "No Python found!"
    exit 1
fi

echo "GUI application finished."
