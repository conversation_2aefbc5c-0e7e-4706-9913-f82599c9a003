{"line_info": {"line_id": "L03", "line_name": "3号线", "description": "地铁3号线站台门模拟数据"}, "mqtt_config": {"server": "*************", "port": 1883, "username": "", "password": "", "qos": 0, "retain": false}, "global_settings": {"send_interval": 1000, "loop_count": 1, "concurrent_stations": 5, "data_variation": 0.1}, "stations": [{"station_id": "S01", "station_name": "车站01", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S02", "station_name": "车站02", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S03", "station_name": "车站03", "enabled": false, "config": {"asd_count": {"sx": 25, "xx": 25}, "eed_count": {"sx": 6, "xx": 6}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 6, "xx": 6}, "psl_count": {"sx": 2, "xx": 2}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}]}