{"line_info": {"line_id": "L03", "line_name": "3号线", "description": "地铁3号线站台门模拟数据"}, "mqtt_config": {"server": "*************", "port": 1883, "username": "metroit", "password": "dtdkws", "qos": 0, "retain": false}, "global_settings": {"send_interval": 15000, "loop_count": 1, "concurrent_stations": 5, "data_variation": 0.1, "show_data_content": true}, "stations": [{"station_id": "S18", "station_name": "大柏树车站", "enabled": true, "config": {"asd_count": {"sx": 1, "xx": 0}, "eed_count": {"sx": 0, "xx": 0}, "msd_count": {"sx": 0, "xx": 0}, "pds_count": {"sx": 0, "xx": 0}, "psl_count": {"sx": 0, "xx": 0}}, "field_configs": {"POWER": {"SQZDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SQBYDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SD_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XDC_WD_GG_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PS": {"XDC_BM_WD": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_NZ": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "ASD": {"LCB_GL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_ZD": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_JDKM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_JDGM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_ZKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_ZGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DWGL_ZT": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SDJS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZAWTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCSJSYC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCU_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "GM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "DJ_SD": {"range": [0, 100], "fixed_value": "", "enabled": true}, "DJ_SCNJ": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDB_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "YC_KM_ML_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "YC_GM_ML_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "EED": {"YJM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YJM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YJM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "MSD": {"CT_DM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_DK_CS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_DK_CS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "IBP": {"IBP_SNXX": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_SQ_PSL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_KM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_GM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PDS": {"JXTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PEDC": {"JKXT_PEDC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XCZX_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZC_HDM_GMSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_YJM_GBSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_JXTC_XT_AQ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}, "GM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "PSL": {"PSL_SN": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_HSJC": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "SIG": {"XH_KM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XH_GM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZW_XH": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HS_JC": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "GB_SJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSD_GD_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "ZW_XH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "KM_ML_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "GM_ML_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "SIG_GD_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "ZCGB_SJXH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "HSJCXH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}}}}]}