"""
测试修正后的toggle逻辑
按照正确的理解：触发循环=N表示每N个循环切换一次
"""
from digital_field_generator import digital_generator

def test_correct_toggle_logic():
    """测试正确的toggle逻辑"""
    print("=== 正确的Toggle逻辑测试 ===\n")
    
    test_cases = [
        {
            'name': '默认值=0, 触发循环=1 (每1个循环切换)',
            'config': {'default_value': 0, 'trigger_loop': 1, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [0, 1, 0, 1, 0, 1, 0, 1, 0, 1],
            'description': '每1个循环切换一次：0,1,0,1,0,1...'
        },
        {
            'name': '默认值=0, 触发循环=2 (每2个循环切换)',
            'config': {'default_value': 0, 'trigger_loop': 2, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
            'description': '每2个循环切换一次：0,0,1,1,0,0,1,1...'
        },
        {
            'name': '默认值=0, 触发循环=3 (每3个循环切换)',
            'config': {'default_value': 0, 'trigger_loop': 3, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [0, 0, 0, 1, 1, 1, 0, 0, 0, 1],
            'description': '每3个循环切换一次：0,0,0,1,1,1,0,0,0,1...'
        },
        {
            'name': '默认值=1, 触发循环=2 (每2个循环切换)',
            'config': {'default_value': 1, 'trigger_loop': 2, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [1, 1, 0, 0, 1, 1, 0, 0, 1, 1],
            'description': '每2个循环切换一次：1,1,0,0,1,1,0,0...'
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases):
        print(f"{i+1}. {test_case['name']}")
        print(f"   说明: {test_case['description']}")
        
        # 重置状态
        digital_generator.reset_field_states()
        
        field_key = f"test_field_{i}"
        results = []
        
        for loop in range(1, 11):
            value = digital_generator.generate_digital_value(
                field_key,
                test_case['config'],
                loop,
                10
            )
            results.append(value)
        
        print(f"   预期: {' -> '.join(map(str, test_case['expected']))}")
        print(f"   实际: {' -> '.join(map(str, results))}")
        
        if results == test_case['expected']:
            print("   ✅ 正确")
        else:
            print("   ❌ 错误")
            all_passed = False
        print()
    
    return all_passed

def test_hdm_ygdw_scenario():
    """测试HDM_YGDW的具体场景"""
    print("=== HDM_YGDW场景测试 ===\n")
    
    # 您的实际配置
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("HDM_YGDW配置: 默认值=0, 触发循环=2, 模式=toggle")
    print("正确理解: 每2个循环切换一次状态")
    print("预期结果: 0, 0, 1, 1, 0, 0, 1, 1, 0, 0")
    print()
    
    # 重置状态
    digital_generator.reset_field_states()
    
    field_key = "HDM_YGDW_test"
    results = []
    
    print("实际结果:")
    for loop in range(1, 11):
        value = digital_generator.generate_digital_value(
            field_key,
            config,
            loop,
            10
        )
        results.append(value)
        print(f"循环 {loop:2d}: {value}")
    
    print(f"\n完整序列: {' -> '.join(map(str, results))}")
    
    # 验证结果
    expected = [0, 0, 1, 1, 0, 0, 1, 1, 0, 0]
    if results == expected:
        print("✅ HDM_YGDW toggle逻辑正确！")
        return True
    else:
        print("❌ HDM_YGDW toggle逻辑仍有问题")
        print(f"预期: {' -> '.join(map(str, expected))}")
        return False

def explain_toggle_logic():
    """解释toggle逻辑"""
    print("=== Toggle逻辑说明 ===\n")
    
    print("正确的Toggle逻辑理解:")
    print("- trigger_loop=1: 每1个循环切换一次 → 0,1,0,1,0,1...")
    print("- trigger_loop=2: 每2个循环切换一次 → 0,0,1,1,0,0,1,1...")
    print("- trigger_loop=3: 每3个循环切换一次 → 0,0,0,1,1,1,0,0,0,1,1,1...")
    print()
    
    print("计算方式:")
    print("1. 计算周期位置: cycle_position = (current_loop - 1) % (trigger_loop * 2)")
    print("2. 如果 cycle_position < trigger_loop: 返回默认值")
    print("3. 否则: 返回相反值")
    print()
    
    print("示例 (默认值=0, 触发循环=2):")
    for loop in range(1, 9):
        cycle_position = (loop - 1) % (2 * 2)  # trigger_loop = 2
        if cycle_position < 2:
            value = 0  # default_value
            phase = "默认值阶段"
        else:
            value = 1  # 1 - default_value
            phase = "切换值阶段"
        
        print(f"  循环{loop}: cycle_position={cycle_position}, {phase} → {value}")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 边界情况测试 ===\n")
    
    edge_cases = [
        {
            'name': 'trigger_loop=0 (无效配置)',
            'config': {'default_value': 0, 'trigger_loop': 0, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [0, 0, 0, 0, 0],
            'description': '触发循环为0，应该始终返回默认值'
        },
        {
            'name': 'trigger_loop=负数 (无效配置)',
            'config': {'default_value': 1, 'trigger_loop': -1, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [1, 1, 1, 1, 1],
            'description': '触发循环为负数，应该始终返回默认值'
        }
    ]
    
    for i, test_case in enumerate(edge_cases):
        print(f"{i+1}. {test_case['name']}")
        print(f"   说明: {test_case['description']}")
        
        digital_generator.reset_field_states()
        field_key = f"edge_test_{i}"
        results = []
        
        for loop in range(1, 6):
            value = digital_generator.generate_digital_value(
                field_key,
                test_case['config'],
                loop,
                5
            )
            results.append(value)
        
        print(f"   预期: {' -> '.join(map(str, test_case['expected']))}")
        print(f"   实际: {' -> '.join(map(str, results))}")
        
        if results == test_case['expected']:
            print("   ✅ 正确")
        else:
            print("   ❌ 错误")
        print()

if __name__ == "__main__":
    print("开始测试修正后的toggle逻辑...\n")
    
    explain_toggle_logic()
    success1 = test_correct_toggle_logic()
    success2 = test_hdm_ygdw_scenario()
    test_edge_cases()
    
    print("=== 测试总结 ===")
    if success1 and success2:
        print("✅ 所有测试通过！Toggle逻辑修正成功！")
        print("\n现在HDM_YGDW字段 (默认值=0, 触发循环=2) 应该显示:")
        print("循环1: 0, 循环2: 0, 循环3: 1, 循环4: 1, 循环5: 0, 循环6: 0, ...")
    else:
        print("❌ 仍有问题需要解决")
    
    print("\n请重新启动应用程序以应用修正！")
