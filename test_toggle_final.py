"""
测试最终的toggle逻辑修复
"""
from digital_field_generator import digital_generator

def test_hdm_ygdw_scenario():
    """测试HDM_YGDW的具体场景"""
    print("=== HDM_YGDW Toggle测试 ===\n")
    
    # 您的实际配置
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("配置: 默认值=0, 触发循环=2, 模式=toggle")
    print("期望结果: 循环1=0, 循环2=1, 循环3=0, 循环4=1, 循环5=0, ...")
    print()
    
    # 重置状态
    digital_generator.reset_field_states()
    
    field_key = "HDM_YGDW_test"
    results = []
    
    print("实际结果:")
    for loop in range(1, 11):
        value = digital_generator.generate_digital_value(
            field_key,
            config,
            loop,
            10
        )
        results.append(value)
        print(f"循环 {loop:2d}: {value}")
    
    print(f"\n完整序列: {' -> '.join(map(str, results))}")
    
    # 验证结果
    expected = [0, 1, 0, 1, 0, 1, 0, 1, 0, 1]
    if results == expected:
        print("✅ Toggle逻辑正确！")
        return True
    else:
        print("❌ Toggle逻辑仍有问题")
        print(f"预期: {' -> '.join(map(str, expected))}")
        print(f"实际: {' -> '.join(map(str, results))}")
        return False

def test_different_configs():
    """测试不同的toggle配置"""
    print("\n=== 不同配置测试 ===\n")
    
    test_cases = [
        {
            'name': '默认值0，第1次循环开始',
            'config': {'default_value': 0, 'trigger_loop': 1, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [1, 0, 1, 0, 1]
        },
        {
            'name': '默认值1，第2次循环开始',
            'config': {'default_value': 1, 'trigger_loop': 2, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [1, 0, 1, 0, 1]
        },
        {
            'name': '默认值0，第3次循环开始',
            'config': {'default_value': 0, 'trigger_loop': 3, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [0, 0, 1, 0, 1]
        },
        {
            'name': '默认值1，第1次循环开始',
            'config': {'default_value': 1, 'trigger_loop': 1, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': [0, 1, 0, 1, 0]
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases):
        print(f"{i+1}. {test_case['name']}")
        
        # 重置状态
        digital_generator.reset_field_states()
        
        field_key = f"test_field_{i}"
        results = []
        
        for loop in range(1, 6):
            value = digital_generator.generate_digital_value(
                field_key,
                test_case['config'],
                loop,
                5
            )
            results.append(value)
        
        print(f"   预期: {' -> '.join(map(str, test_case['expected']))}")
        print(f"   实际: {' -> '.join(map(str, results))}")
        
        if results == test_case['expected']:
            print("   ✅ 正确")
        else:
            print("   ❌ 错误")
            all_passed = False
        print()
    
    return all_passed

def test_multiple_calls():
    """测试多次调用的一致性"""
    print("=== 多次调用一致性测试 ===\n")
    
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("测试同一个字段多次调用是否一致")
    print("配置: 默认值=0, 触发循环=2, 模式=toggle")
    print()
    
    field_key = "consistency_test"
    
    # 第一次调用序列
    digital_generator.reset_field_states()
    results1 = []
    for loop in range(1, 6):
        value = digital_generator.generate_digital_value(field_key, config, loop, 5)
        results1.append(value)
    
    # 第二次调用序列（重置状态）
    digital_generator.reset_field_states()
    results2 = []
    for loop in range(1, 6):
        value = digital_generator.generate_digital_value(field_key, config, loop, 5)
        results2.append(value)
    
    print(f"第一次调用: {' -> '.join(map(str, results1))}")
    print(f"第二次调用: {' -> '.join(map(str, results2))}")
    
    if results1 == results2:
        print("✅ 多次调用结果一致")
        return True
    else:
        print("❌ 多次调用结果不一致")
        return False

def simulate_real_scenario():
    """模拟真实场景"""
    print("\n=== 模拟真实场景 ===\n")
    
    print("模拟您的实际情况:")
    print("- 车站S18")
    print("- ASD设备: SX方向30个, XX方向30个")
    print("- HDM_YGDW字段配置: 默认值=0, 触发循环=2, 模式=toggle")
    print("- 总循环次数: 10")
    print()
    
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    # 重置状态
    digital_generator.reset_field_states()
    
    # 测试几个ASD设备
    devices = [
        ("SX", "ASD01"),
        ("SX", "ASD02"),
        ("XX", "ASD01"),
        ("XX", "ASD02")
    ]
    
    print("前4个ASD设备的HDM_YGDW字段值:")
    print("循环  SX_ASD01  SX_ASD02  XX_ASD01  XX_ASD02")
    
    for loop in range(1, 11):
        values = []
        for direction, device in devices:
            field_key = f"S18_{direction}_{device}_HDM_YGDW"
            value = digital_generator.generate_digital_value(field_key, config, loop, 10)
            values.append(str(value))
        
        print(f"  {loop:2d}      {values[0]}        {values[1]}        {values[2]}        {values[3]}")
    
    print("\n如果所有设备的HDM_YGDW都显示相同的切换模式，说明修复成功！")

if __name__ == "__main__":
    print("开始测试最终的toggle逻辑修复...\n")
    
    success1 = test_hdm_ygdw_scenario()
    success2 = test_different_configs()
    success3 = test_multiple_calls()
    
    simulate_real_scenario()
    
    print("\n=== 测试总结 ===")
    if success1 and success2 and success3:
        print("✅ 所有测试通过！Toggle逻辑修复成功！")
        print("\n现在您应该看到:")
        print("- 循环1: 0 (默认值，未到触发循环)")
        print("- 循环2: 1 (第一次触发)")
        print("- 循环3: 0 (切换)")
        print("- 循环4: 1 (切换)")
        print("- 循环5: 0 (切换)")
        print("- ...")
    else:
        print("❌ 仍有问题需要解决")
    
    print("\n请重新启动应用程序以应用修复！")
