# main.py
import tkinter as tk
from tkinter import messagebox
import paho.mqtt.client as mqtt
import json
import random
import time
from threading import Thread
import logging

class MQTTDataGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("站台门模拟数据发生器")
        self.root.geometry("500x600")  # 设置窗口大小

        # 强制窗口显示在前台
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
        
        # 服务器配置
        self.server_label = tk.Label(root, text="MQTT服务器地址:")
        self.server_label.pack()
        self.server_entry = tk.Entry(root)
        self.server_entry.insert(0, "49.235.38.216")
        self.server_entry.pack()
        self.port_label = tk.Label(root, text="MQTT服务器端口:")
        self.port_label.pack()
        self.port_entry = tk.Entry(root)
        self.port_entry.insert(0, "1883")
        self.port_entry.pack()

        # 添加用户名和密码输入框
        self.username_label = tk.Label(root, text="MQTT用户名:")
        self.username_label.pack()
        self.username_entry = tk.Entry(root)
        self.username_entry.pack()

        self.password_label = tk.Label(root, text="MQTT密码:")
        self.password_label.pack()
        self.password_entry = tk.Entry(root, show="*")
        self.password_entry.pack()

        # 添加线路编号和车站编号输入框
        self.line_label = tk.Label(root, text="线路编号:")
        self.line_label.pack()
        self.line_entry = tk.Entry(root)
        self.line_entry.insert(0, "L03")
        self.line_entry.pack()

        self.station_label = tk.Label(root, text="车站编号:")
        self.station_label.pack()
        self.station_entry = tk.Entry(root)
        self.station_entry.insert(0, "S18")
        self.station_entry.pack()

        # 添加循环次数输入框
        self.loop_count_label = tk.Label(root, text="循环次数:")
        self.loop_count_label.pack()
        self.loop_count_entry = tk.Entry(root)
        self.loop_count_entry.insert(0, "1")
        self.loop_count_entry.pack()

        # 控制按钮
        self.start_button = tk.Button(root, text="开始发送", command=self.start_sending)
        self.start_button.pack()
        self.stop_button = tk.Button(root, text="停止发送", command=self.stop_sending, state=tk.DISABLED)
        self.stop_button.pack()
        
        # 添加日志记录
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        
        # 添加状态显示文本框
        self.status_text = tk.Text(root, height=10, width=50)
        self.status_text.pack()
        
        self.client = None
        self.running = False
        self.thread = None

    def start_sending(self):
        server = self.server_entry.get()
        port = int(self.port_entry.get())
        username = self.username_entry.get()
        password = self.password_entry.get()
        loop_count = int(self.loop_count_entry.get())  # 获取循环次数

        try:
            self.client = mqtt.Client()
            if username and password:
                self.client.username_pw_set(username, password)
            self.client.connect(server, port)
            self.client.loop_start()
            
            self.running = True
            self.thread = Thread(target=self.send_data, args=(loop_count,))  # 传递循环次数
            self.thread.start()
            
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
        except Exception as e:
            messagebox.showerror("连接错误", str(e))

    def stop_sending(self):
        self.running = False
        if self.thread:
            self.thread.join()
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
        
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def send_data(self, loop_count):
        for loop in range(loop_count):
            start_time = time.time()
            messages = self.generate_data()
            for msg in messages:
                topic = msg["topic"]
                payload = json.dumps(msg["payload"])
                
                # 记录发送前信息
                log_msg = f"准备发送消息到主题 {topic}: {payload} (循环 {loop + 1}/{loop_count})"
                logging.info(log_msg)
                self.status_text.insert(tk.END, log_msg + "\n")
                self.status_text.see(tk.END)
                
                result = self.client.publish(topic, payload)
                if result.rc == mqtt.MQTT_ERR_SUCCESS:
                    log_msg = f"消息成功发送到主题 {topic} (循环 {loop + 1}/{loop_count})"
                else:
                    log_msg = f"消息发送失败到主题 {topic}, 错误码: {result.rc} (循环 {loop + 1}/{loop_count})"
                
                # 记录发送结果
                logging.info(log_msg)
                self.status_text.insert(tk.END, log_msg + "\n")
                self.status_text.see(tk.END)
        
        # 发送完成后恢复按钮状态
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        log_msg = f"数据发送完成，耗时: {elapsed_time:.2f} 秒"
        logging.info(log_msg)
        self.status_text.insert(tk.END, log_msg + "\n")
        self.status_text.see(tk.END)
        
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()

    def generate_data(self):
        # 参考message.json生成数据
        timestamp = int(time.time() * 1000)
        line = self.line_entry.get()
        station = self.station_entry.get()

        power_msg = {
            "topic": f"{line}/{station}/PSD/POWER",
            "payload": {
                "timestamp": timestamp,
                "SQZDY_TR": random.randint(0, 1),
                "SQBYDY_TR": random.randint(0, 1),
                "SD_GZ": random.randint(0, 1),
                "QDDY_GZ": random.randint(0, 1),
                "KZDY_GZ": random.randint(0, 1),
                "QDDY_DY_BJ": random.randint(0, 1),
                "KZDY_DY_BJ": random.randint(0, 1),
                "QDDC_TR_BJ": random.randint(0, 1),
                "KZDC_TR_BJ": random.randint(0, 1),
                "QDDC_DY_BJ": random.randint(0, 1),
                "KZDC_DY_BJ": random.randint(0, 1),
                "XDC_WD_GG_BJ": random.randint(0, 1),
                "SX_QDDY_HL1_BJ": random.randint(0, 1),
                "SX_QDDY_HL2_BJ": random.randint(0, 1),
                "SX_QDDY_HL3_BJ": random.randint(0, 1),
                "SX_QDDY_HL4_BJ": random.randint(0, 1),
                "SX_QDDY_HL5_BJ": random.randint(0, 1),
                "SX_KZDY_SIG_HL_BJ": random.randint(0, 1),
                "SX_KZDY_PSL_HL_BJ": random.randint(0, 1),
                "SX_KZDY_IBP_HL_BJ": random.randint(0, 1),
                "SX_KZDY_PEDC_HL_BJ": random.randint(0, 1),
                "SX_KZDY_DCU_HL_BJ": random.randint(0, 1),
                "SX_KZDY_AQ_HL_BJ": random.randint(0, 1),
                "SX_KZDY_JXTC_HL_BJ": random.randint(0, 1),
                "SX_KZDY_DKD_HL_BJ": random.randint(0, 1),
                "SX_KZDY_LWD_HL_BJ": random.randint(0, 1),
                "XX_QDDY_HL1_BJ": random.randint(0, 1),
                "XX_QDDY_HL2_BJ": random.randint(0, 1),
                "XX_QDDY_HL3_BJ": random.randint(0, 1),
                "XX_QDDY_HL4_BJ": random.randint(0, 1),
                "XX_QDDY_HL5_BJ": random.randint(0, 1),
                "XX_KZDY_SIG_HL_BJ": random.randint(0, 1),
                "XX_KZDY_PSL_HL_BJ": random.randint(0, 1),
                "XX_KZDY_IBP_HL_BJ": random.randint(0, 1),
                "XX_KZDY_PEDC_HL_BJ": random.randint(0, 1),
                "XX_KZDY_DCU_HL_BJ": random.randint(0, 1),
                "XX_KZDY_AQ_HL_BJ": random.randint(0, 1),
                "XX_KZDY_JXTC_HL_BJ": random.randint(0, 1),
                "XX_KZDY_DKD_HL_BJ": random.randint(0, 1),
                "XX_KZDY_LWD_HL_BJ": random.randint(0, 1)
            }
        }
        
        ps_msg = {
            "topic": f"{line}/{station}/PSD/PS",
            "payload": {
                "timestamp": timestamp,
                "XDC_BM_WD": random.randint(0, 100),
                "XDC_NZ": random.randint(0, 100),
                "XDC_DY": random.randint(0, 100),
                "QDDY_SC_DL": random.randint(0, 100),
                "KZDY_SC_DL": random.randint(0, 100),
                "QDDY_SC_DY": random.randint(0, 100),
                "KZDY_SC_DY": random.randint(0, 100)
            }
        }

        messages = [power_msg, ps_msg]

        # 添加SX方向的ASD消息
        for i in range(1, 31):
            asd_msg = {
                "topic": f"{line}/{station}/PSD/SX/ASD{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "LCB_GL": random.randint(0, 1),
                    "LCB_ZD": random.randint(0, 1),
                    "LCB_JDKM": random.randint(0, 1),
                    "LCB_JDGM": random.randint(0, 1),
                    "HDM_ZKDW": random.randint(0, 1),
                    "HDM_YKDW": random.randint(0, 1),
                    "HDM_ZGDW": random.randint(0, 1),
                    "HDM_YGDW": random.randint(0, 1),
                    "YC_KM": random.randint(0, 1),
                    "YC_GM": random.randint(0, 1),
                    "DWGL_ZT": random.randint(0, 1),
                    "SDJS_BJ": random.randint(0, 1),
                    "ZAWTC_BJ": random.randint(0, 1),
                    "DCSJSYC_BJ": random.randint(0, 1),
                    "DCU_GZ": random.randint(0, 1),
                    "ZXWKG_GZ": random.randint(0, 1),
                    "YXWKG_GZ": random.randint(0, 1),
                    "DJ_GZ": random.randint(0, 1),
                    "GM_GZ": random.randint(0, 1),
                    "KM_GZ": random.randint(0, 1),
                    "DJ_DL": random.randint(0, 100),
                    "DJ_SD": random.randint(0, 100),
                    "DJ_SCNJ": random.randint(0, 100),
                    "QDB_DY": random.randint(0, 100),
                    "YC_KM_ML_DY": random.randint(0, 100),
                    "YC_GM_ML_DY": random.randint(0, 100)
                }
            }
            messages.append(asd_msg)

        # 添加XX方向的ASD消息
        for i in range(1, 31):
            asd_msg = {
                "topic": f"{line}/{station}/PSD/XX/ASD{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "LCB_GL": random.randint(0, 1),
                    "LCB_ZD": random.randint(0, 1),
                    "LCB_JDKM": random.randint(0, 1),
                    "LCB_JDGM": random.randint(0, 1),
                    "HDM_ZKDW": random.randint(0, 1),
                    "HDM_YKDW": random.randint(0, 1),
                    "HDM_ZGDW": random.randint(0, 1),
                    "HDM_YGDW": random.randint(0, 1),
                    "YC_KM": random.randint(0, 1),
                    "YC_GM": random.randint(0, 1),
                    "DWGL_ZT": random.randint(0, 1),
                    "SDJS_BJ": random.randint(0, 1),
                    "ZAWTC_BJ": random.randint(0, 1),
                    "DCSJSYC_BJ": random.randint(0, 1),
                    "DCU_GZ": random.randint(0, 1),
                    "ZXWKG_GZ": random.randint(0, 1),
                    "YXWKG_GZ": random.randint(0, 1),
                    "DJ_GZ": random.randint(0, 1),
                    "GM_GZ": random.randint(0, 1),
                    "KM_GZ": random.randint(0, 1),
                    "DJ_DL": random.randint(0, 100),
                    "DJ_SD": random.randint(0, 100),
                    "DJ_SCNJ": random.randint(0, 100),
                    "QDB_DY": random.randint(0, 100),
                    "YC_KM_ML_DY": random.randint(0, 100),
                    "YC_GM_ML_DY": random.randint(0, 100)
                }
            }
            messages.append(asd_msg)

        # 添加eed消息
        for i in range(1, 9):
            eed_msg = {
                "topic": f"{line}/{station}/PSD/SX/EED{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "EED_STATUS": random.randint(0, 1),
                    "EED_TEMPERATURE": random.randint(0, 100),
                    "EED_VOLTAGE": random.randint(0, 100)
                }
            }
            messages.append(eed_msg)

        # 添加eed消息
        for i in range(1, 9):
            eed_msg = {
                "topic": f"{line}/{station}/PSD/XX/EED{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "EED_STATUS": random.randint(0, 1),
                    "EED_TEMPERATURE": random.randint(0, 100),
                    "EED_VOLTAGE": random.randint(0, 100)
                }
            }
            messages.append(eed_msg)
        # 添加msd消息
        for i in range(1, 3):
            msd_msg = {
                "topic": f"{line}/{station}/PSD/SX/MSD{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "MSD_STATUS": random.randint(0, 1),
                    "MSD_PRESSURE": random.randint(0, 100),
                    "MSD_FLOW": random.randint(0, 100)
                }
            }
            messages.append(msd_msg)

        # 添加msd消息
        for i in range(1, 3):
            msd_msg = {
                "topic": f"{line}/{station}/PSD/XX/MSD{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "MSD_STATUS": random.randint(0, 1),
                    "MSD_PRESSURE": random.randint(0, 100),
                    "MSD_FLOW": random.randint(0, 100)
                }
            }
        messages.append(msd_msg)   

        # 添加msd消息
        for i in range(1, 3):
            msd_msg = {
                "topic": f"{line}/{station}/PSD/XX/MSD{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "MSD_STATUS": random.randint(0, 1),
                    "MSD_PRESSURE": random.randint(0, 100),
                    "MSD_FLOW": random.randint(0, 100)
                }
            }
            messages.append(msd_msg)

        # 添加IBP消息
        ibp_msg = {
            "topic": f"{line}/{station}/PSD/SX/IBP",
            "payload": {
                "timestamp": timestamp,
                "IBP_SNXX": random.randint(0, 1),
                "IBP_SQ_PSL": random.randint(0, 1),
                "IBP_KM_ML": random.randint(0, 1),
                "IBP_GM_ML": random.randint(0, 1)
            }
        }
        messages.append(ibp_msg)

        # 添加XX方向的IBP消息
        ibp_msg_xx = {
            "topic": f"{line}/{station}/PSD/XX/IBP",
            "payload": {
                "timestamp": timestamp,
                "IBP_SNXX": random.randint(0, 1),
                "IBP_SQ_PSL": random.randint(0, 1),
                "IBP_KM_ML": random.randint(0, 1),
                "IBP_GM_ML": random.randint(0, 1)
            }
        }
        messages.append(ibp_msg_xx)

        # 添加PDS01~08消息
        for i in range(1, 9):
            pds_msg = {
                "topic": f"{line}/{station}/PSD/SX/PDS{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "JXTC_BJ": random.randint(0, 1),
                    "JXTC_PL": random.randint(0, 1),
                    "JXTC_GZ": random.randint(0, 1)
                }
            }
            messages.append(pds_msg)

        # 添加XX方向的PDS01~08消息
        for i in range(1, 9):
            pds_msg_xx = {
                "topic": f"{line}/{station}/PSD/XX/PDS{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "JXTC_BJ": random.randint(0, 1),
                    "JXTC_PL": random.randint(0, 1),
                    "JXTC_GZ": random.randint(0, 1)
                }
            }
            messages.append(pds_msg_xx)

        # 添加PEDC消息
        pedc_msg = {
            "topic": f"{line}/{station}/PSD/SX/PEDC",
            "payload": {
                "timestamp": timestamp,
                "JKXT_PEDC_GZ": random.randint(0, 1),
                "XCZX_GZ": random.randint(0, 1),
                "ZC_HDM_GMSJ_XH_DY": random.randint(0, 100),
                "ZC_YJM_GBSJ_XH_DY": random.randint(0, 100),
                "ZC_JXTC_XT_AQ_XH_DY": random.randint(0, 100),
                "KM_ML_SCJDQ_JC": random.randint(0, 100),
                "GM_ML_SCJDQ_JC": random.randint(0, 100)
            }
        }
        messages.append(pedc_msg)

        # 添加XX方向的PEDC消息
        pedc_msg_xx = {
            "topic": f"{line}/{station}/PSD/XX/PEDC",
            "payload": {
                "timestamp": timestamp,
                "JKXT_PEDC_GZ": random.randint(0, 1),
                "XCZX_GZ": random.randint(0, 1),
                "ZC_HDM_GMSJ_XH_DY": random.randint(0, 100),
                "ZC_YJM_GBSJ_XH_DY": random.randint(0, 100),
                "ZC_JXTC_XT_AQ_XH_DY": random.randint(0, 100),
                "KM_ML_SCJDQ_JC": random.randint(0, 100),
                "GM_ML_SCJDQ_JC": random.randint(0, 100)
            }
        }
        messages.append(pedc_msg_xx)

        # 添加PSL01~03消息
        for i in range(1, 4):
            psl_msg = {
                "topic": f"{line}/{station}/PSD/SX/PSL{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "PSL_SN": random.randint(0, 1),
                    "PSL_KM": random.randint(0, 1),
                    "PSL_GM": random.randint(0, 1),
                    "PSL_HSJC": random.randint(0, 1)
                }
            }
            messages.append(psl_msg)

        # 添加XX方向的PSL01~03消息
        for i in range(1, 4):
            psl_msg_xx = {
                "topic": f"{line}/{station}/PSD/XX/PSL{i:02d}",
                "payload": {
                    "timestamp": timestamp,
                    "PSL_SN": random.randint(0, 1),
                    "PSL_KM": random.randint(0, 1),
                    "PSL_GM": random.randint(0, 1),
                    "PSL_HSJC": random.randint(0, 1)
                }
            }
            messages.append(psl_msg_xx)

        # 添加SIG消息
        sig_msg = {
            "topic": f"{line}/{station}/PSD/SX/SIG",
            "payload": {
                "timestamp": timestamp,
                "SIG_STATUS": random.randint(0, 1),
                "SIG_ALARM": random.randint(0, 1)
            }
        }
        messages.append(sig_msg)

        # 添加XX方向的SIG消息
        sig_msg_xx = {
            "topic": f"{line}/{station}/PSD/XX/SIG",
            "payload": {
                "timestamp": timestamp,
                "SIG_STATUS": random.randint(0, 1),
                "SIG_ALARM": random.randint(0, 1)
            }
        }
        messages.append(sig_msg_xx)

        return messages
if __name__ == "__main__":
    print("正在启动MQTT数据发生器...")
    try:
        root = tk.Tk()
        print("Tkinter窗口已创建")
        app = MQTTDataGenerator(root)
        print("应用程序已初始化，启动GUI...")
        root.mainloop()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()