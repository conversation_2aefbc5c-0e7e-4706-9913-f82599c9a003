"""
测试HDM_YGDW字段的toggle配置
"""
from digital_field_generator import digital_generator
import json

def test_hdm_ygdw_config():
    """测试HDM_YGDW字段配置"""
    print("=== HDM_YGDW字段Toggle测试 ===\n")
    
    # 从stations.json读取实际配置
    try:
        with open("stations.json", "r", encoding="utf-8") as f:
            config_data = json.load(f)
        
        station_config = config_data["stations"][0]  # 第一个车站
        hdm_ygdw_config = station_config["field_configs"]["ASD"]["HDM_YGDW"]
        loop_count = config_data["global_settings"]["loop_count"]
        
        print(f"车站: {station_config['station_name']} ({station_config['station_id']})")
        print(f"HDM_YGDW配置: {hdm_ygdw_config}")
        print(f"总循环次数: {loop_count}")
        print()
        
        # 重置状态
        digital_generator.reset_field_states()
        
        # 模拟数据生成
        field_key = f"{station_config['station_id']}_HDM_YGDW"
        results = []
        
        print("循环结果:")
        for loop in range(1, loop_count + 1):
            value = digital_generator.generate_digital_value(
                field_key,
                hdm_ygdw_config,
                loop,
                loop_count
            )
            results.append(str(value))
            print(f"循环 {loop:2d}: {value}")
        
        print(f"\n完整序列: {' -> '.join(results)}")
        
        # 分析结果
        print("\n=== 结果分析 ===")
        changes = 0
        for i in range(1, len(results)):
            if results[i] != results[i-1]:
                changes += 1
        
        print(f"状态变化次数: {changes}")
        print(f"预期变化次数: {loop_count - hdm_ygdw_config['trigger_loop']}")
        
        if changes > 0:
            print("✅ Toggle模式工作正常！")
        else:
            print("❌ Toggle模式未生效，请检查配置")
            
    except Exception as e:
        print(f"读取配置失败: {e}")

def test_multiple_asd_devices():
    """测试多个ASD设备的HDM_YGDW字段"""
    print("\n=== 多个ASD设备测试 ===\n")
    
    try:
        with open("stations.json", "r", encoding="utf-8") as f:
            config_data = json.load(f)
        
        station_config = config_data["stations"][0]
        hdm_ygdw_config = station_config["field_configs"]["ASD"]["HDM_YGDW"]
        asd_count_sx = station_config["config"]["asd_count"]["sx"]
        asd_count_xx = station_config["config"]["asd_count"]["xx"]
        
        print(f"上行ASD设备数量: {asd_count_sx}")
        print(f"下行ASD设备数量: {asd_count_xx}")
        print()
        
        # 重置状态
        digital_generator.reset_field_states()
        
        # 测试前3个设备的前5个循环
        test_loops = 5
        test_devices = 3
        
        print("前3个ASD设备的HDM_YGDW字段（前5个循环）:")
        print("设备\\循环  1  2  3  4  5")
        
        for direction in ["SX", "XX"]:
            print(f"\n{direction}方向:")
            for device_num in range(1, min(test_devices + 1, asd_count_sx + 1)):
                field_key = f"{station_config['station_id']}_{direction}_ASD{device_num:02d}_HDM_YGDW"
                results = []
                
                for loop in range(1, test_loops + 1):
                    value = digital_generator.generate_digital_value(
                        field_key,
                        hdm_ygdw_config,
                        loop,
                        test_loops
                    )
                    results.append(str(value))
                
                print(f"ASD{device_num:02d}      {' '.join(results)}")
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_hdm_ygdw_config()
    test_multiple_asd_devices()
    
    print("\n=== 使用提示 ===")
    print("1. 确保在应用中点击'开始发送'来启动数据发送")
    print("2. 每次重新发送时，状态会自动重置")
    print("3. 可以通过MQTT客户端订阅主题来查看实际数据:")
    print("   - L03/S18/PSD/SX/ASD01")
    print("   - L03/S18/PSD/XX/ASD01")
    print("   - 等等...")
    print("4. 在message.json中查看HDM_YGDW字段的值变化")
