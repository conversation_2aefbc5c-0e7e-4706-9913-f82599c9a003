"""
多车站MQTT数据发生器GUI
支持多车站配置和数据发送
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import threading
import time
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from data_generator import StationDataGenerator
import paho.mqtt.client as mqtt
import logging


class StationConfigDialog:
    """车站配置对话框"""
    
    def __init__(self, parent, station_config=None):
        self.parent = parent
        self.result = None
        self.station_config = station_config or {
            "station_id": "",
            "station_name": "",
            "enabled": True,
            "config": {
                "asd_count": {"sx": 30, "xx": 30},
                "eed_count": {"sx": 8, "xx": 8},
                "msd_count": {"sx": 2, "xx": 2},
                "pds_count": {"sx": 8, "xx": 8},
                "psl_count": {"sx": 3, "xx": 3}
            }
        }
        
        self.create_dialog()
    
    def create_dialog(self):
        """创建配置对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("车站配置")
        self.dialog.geometry("400x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 基本信息
        basic_frame = ttk.LabelFrame(self.dialog, text="基本信息")
        basic_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(basic_frame, text="车站编号:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.station_id_var = tk.StringVar(value=self.station_config["station_id"])
        ttk.Entry(basic_frame, textvariable=self.station_id_var).grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(basic_frame, text="车站名称:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.station_name_var = tk.StringVar(value=self.station_config["station_name"])
        ttk.Entry(basic_frame, textvariable=self.station_name_var).grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        
        self.enabled_var = tk.BooleanVar(value=self.station_config["enabled"])
        ttk.Checkbutton(basic_frame, text="启用", variable=self.enabled_var).grid(row=2, column=0, columnspan=2, sticky="w", padx=5, pady=2)
        
        basic_frame.columnconfigure(1, weight=1)
        
        # 设备配置
        device_frame = ttk.LabelFrame(self.dialog, text="设备数量配置")
        device_frame.pack(fill="x", padx=10, pady=5)
        
        devices = ["asd", "eed", "msd", "pds", "psl"]
        device_labels = {"asd": "ASD设备", "eed": "EED设备", "msd": "MSD设备", "pds": "PDS设备", "psl": "PSL设备"}
        
        self.device_vars = {}
        for i, device in enumerate(devices):
            ttk.Label(device_frame, text=f"{device_labels[device]}:").grid(row=i, column=0, sticky="w", padx=5, pady=2)
            
            # 上行数量
            ttk.Label(device_frame, text="上行:").grid(row=i, column=1, sticky="w", padx=5, pady=2)
            sx_var = tk.IntVar(value=self.station_config["config"][f"{device}_count"]["sx"])
            ttk.Entry(device_frame, textvariable=sx_var, width=5).grid(row=i, column=2, padx=5, pady=2)
            
            # 下行数量
            ttk.Label(device_frame, text="下行:").grid(row=i, column=3, sticky="w", padx=5, pady=2)
            xx_var = tk.IntVar(value=self.station_config["config"][f"{device}_count"]["xx"])
            ttk.Entry(device_frame, textvariable=xx_var, width=5).grid(row=i, column=4, padx=5, pady=2)
            
            self.device_vars[device] = {"sx": sx_var, "xx": xx_var}
        
        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(button_frame, text="确定", command=self.save_config).pack(side="right", padx=5)
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side="right", padx=5)
        ttk.Button(button_frame, text="字段配置", command=self.configure_fields).pack(side="left", padx=5)
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                "station_id": self.station_id_var.get(),
                "station_name": self.station_name_var.get(),
                "enabled": self.enabled_var.get(),
                "config": {}
            }
            
            for device, vars_dict in self.device_vars.items():
                config["config"][f"{device}_count"] = {
                    "sx": vars_dict["sx"].get(),
                    "xx": vars_dict["xx"].get()
                }
            
            # 添加默认字段配置
            config["field_configs"] = self.get_default_field_configs()
            
            self.result = config
            self.dialog.destroy()
        except Exception as e:
            messagebox.showerror("错误", f"配置保存失败: {e}")

    def configure_fields(self):
        """配置数据字段"""
        try:
            # 创建临时配置对象
            temp_config = {
                "station_id": self.station_id_var.get(),
                "station_name": self.station_name_var.get(),
                "field_configs": self.station_config.get("field_configs", {})
            }

            from field_config_dialog import FieldConfigDialog
            dialog = FieldConfigDialog(self.dialog, temp_config)
            self.dialog.wait_window(dialog.dialog)

            if dialog.result:
                # 更新当前配置的字段配置
                self.station_config["field_configs"] = dialog.result
                messagebox.showinfo("成功", "字段配置已更新")

        except Exception as e:
            messagebox.showerror("错误", f"字段配置失败: {e}")

    def get_default_field_configs(self):
        """获取默认字段配置"""
        from data_fields_config import DATA_FIELDS_CONFIG

        field_configs = {}

        for device_type, device_config in DATA_FIELDS_CONFIG.items():
            field_configs[device_type] = {}

            for field_name, field_info in device_config["fields"].items():
                if field_info["type"] == "digital":
                    # 开关量字段配置
                    field_configs[device_type][field_name] = {
                        "default_value": field_info.get("default_value", 0),
                        "trigger_loop": 0,
                        "trigger_mode": "once",
                        "fixed_value": "",
                        "enabled": True
                    }
                else:
                    # 数字量字段配置
                    default_range = field_info.get("default", [0, 100])
                    field_configs[device_type][field_name] = {
                        "range": default_range,
                        "fixed_value": "",
                        "enabled": True
                    }

        return field_configs


class MultiStationMQTTGenerator:
    """多车站MQTT数据发生器主界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("多车站MQTT数据发生器")
        self.root.geometry("1000x700")
        
        # 强制窗口显示在前台
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
        
        # 初始化变量
        self.config_data = self.load_default_config()
        self.data_generator = StationDataGenerator()
        self.mqtt_client = None
        self.sending_thread = None
        self.is_sending = False
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        
        self.create_widgets()
        self.refresh_station_list()
    
    def load_default_config(self):
        """加载默认配置"""
        try:
            with open("station_config.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                "line_info": {"line_id": "L03", "line_name": "3号线"},
                "mqtt_config": {"server": "49.235.38.216", "port": 1883, "username": "", "password": ""},
                "global_settings": {"send_interval": 1000, "loop_count": 1, "concurrent_stations": 5},
                "stations": []
            }
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.PanedWindow(self.root, orient="horizontal")
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 左侧面板 - 配置和控制
        left_frame = ttk.Frame(main_frame)
        main_frame.add(left_frame, weight=1)
        
        # 右侧面板 - 车站列表和日志
        right_frame = ttk.Frame(main_frame)
        main_frame.add(right_frame, weight=2)
        
        self.create_config_panel(left_frame)
        self.create_station_panel(right_frame)
        self.create_log_panel(right_frame)
    
    def create_config_panel(self, parent):
        """创建配置面板"""
        config_frame = ttk.LabelFrame(parent, text="配置")
        config_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # MQTT配置
        mqtt_frame = ttk.LabelFrame(config_frame, text="MQTT配置")
        mqtt_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(mqtt_frame, text="服务器:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.server_var = tk.StringVar(value=self.config_data["mqtt_config"]["server"])
        ttk.Entry(mqtt_frame, textvariable=self.server_var).grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(mqtt_frame, text="端口:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.port_var = tk.IntVar(value=self.config_data["mqtt_config"]["port"])
        ttk.Entry(mqtt_frame, textvariable=self.port_var).grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(mqtt_frame, text="用户名:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.username_var = tk.StringVar(value=self.config_data["mqtt_config"]["username"])
        ttk.Entry(mqtt_frame, textvariable=self.username_var).grid(row=2, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(mqtt_frame, text="密码:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.password_var = tk.StringVar(value=self.config_data["mqtt_config"]["password"])
        ttk.Entry(mqtt_frame, textvariable=self.password_var, show="*").grid(row=3, column=1, sticky="ew", padx=5, pady=2)
        
        mqtt_frame.columnconfigure(1, weight=1)
        
        # 线路配置
        line_frame = ttk.LabelFrame(config_frame, text="线路配置")
        line_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(line_frame, text="线路编号:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.line_id_var = tk.StringVar(value=self.config_data["line_info"]["line_id"])
        ttk.Entry(line_frame, textvariable=self.line_id_var).grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(line_frame, text="线路名称:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.line_name_var = tk.StringVar(value=self.config_data["line_info"]["line_name"])
        ttk.Entry(line_frame, textvariable=self.line_name_var).grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        
        line_frame.columnconfigure(1, weight=1)
        
        # 发送配置
        send_frame = ttk.LabelFrame(config_frame, text="发送配置")
        send_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(send_frame, text="循环次数:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.loop_count_var = tk.IntVar(value=self.config_data["global_settings"]["loop_count"])
        ttk.Entry(send_frame, textvariable=self.loop_count_var).grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(send_frame, text="发送间隔(ms):").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.interval_var = tk.IntVar(value=self.config_data["global_settings"]["send_interval"])
        ttk.Entry(send_frame, textvariable=self.interval_var).grid(row=1, column=1, sticky="ew", padx=5, pady=2)

        ttk.Label(send_frame, text="并发车站数:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.concurrent_var = tk.IntVar(value=self.config_data["global_settings"].get("concurrent_stations", 5))
        ttk.Entry(send_frame, textvariable=self.concurrent_var).grid(row=2, column=1, sticky="ew", padx=5, pady=2)

        # 添加日志详细程度控制
        self.show_data_content_var = tk.BooleanVar(value=self.config_data["global_settings"].get("show_data_content", True))
        ttk.Checkbutton(send_frame, text="显示发送数据内容", variable=self.show_data_content_var).grid(row=3, column=0, columnspan=2, sticky="w", padx=5, pady=2)

        # 添加日志详细级别选择
        ttk.Label(send_frame, text="日志详细级别:").grid(row=4, column=0, sticky="w", padx=5, pady=2)
        self.log_detail_var = tk.StringVar(value=self.config_data["global_settings"].get("log_detail_level", "智能"))
        log_detail_combo = ttk.Combobox(send_frame, textvariable=self.log_detail_var, values=["简单", "智能", "详细", "完整"], state="readonly")
        log_detail_combo.grid(row=4, column=1, sticky="ew", padx=5, pady=2)

        send_frame.columnconfigure(1, weight=1)
        
        # 控制按钮
        control_frame = ttk.Frame(config_frame)
        control_frame.pack(fill="x", padx=5, pady=10)
        
        self.start_button = ttk.Button(control_frame, text="开始发送", command=self.start_sending)
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止发送", command=self.stop_sending, state="disabled")
        self.stop_button.pack(side="left", padx=5)
        
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side="right", padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).pack(side="right", padx=5)

    def create_station_panel(self, parent):
        """创建车站管理面板"""
        station_frame = ttk.LabelFrame(parent, text="车站管理")
        station_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 工具栏
        toolbar = ttk.Frame(station_frame)
        toolbar.pack(fill="x", padx=5, pady=5)

        ttk.Button(toolbar, text="添加车站", command=self.add_station).pack(side="left", padx=5)
        ttk.Button(toolbar, text="编辑车站", command=self.edit_station).pack(side="left", padx=5)
        ttk.Button(toolbar, text="删除车站", command=self.delete_station).pack(side="left", padx=5)
        ttk.Button(toolbar, text="字段配置", command=self.configure_fields).pack(side="left", padx=5)
        ttk.Button(toolbar, text="批量启用", command=self.batch_enable).pack(side="left", padx=5)
        ttk.Button(toolbar, text="批量禁用", command=self.batch_disable).pack(side="left", padx=5)

        # 车站列表
        list_frame = ttk.Frame(station_frame)
        list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建Treeview
        columns = ("station_id", "station_name", "enabled", "asd_count", "status")
        self.station_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)

        # 设置列标题
        self.station_tree.heading("station_id", text="车站编号")
        self.station_tree.heading("station_name", text="车站名称")
        self.station_tree.heading("enabled", text="状态")
        self.station_tree.heading("asd_count", text="ASD数量")
        self.station_tree.heading("status", text="发送状态")

        # 设置列宽
        self.station_tree.column("station_id", width=80)
        self.station_tree.column("station_name", width=120)
        self.station_tree.column("enabled", width=60)
        self.station_tree.column("asd_count", width=80)
        self.station_tree.column("status", width=100)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.station_tree.yview)
        self.station_tree.configure(yscrollcommand=scrollbar.set)

        self.station_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定双击事件
        self.station_tree.bind("<Double-1>", lambda e: self.edit_station())

    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = ttk.LabelFrame(parent, text="发送日志")
        log_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.log_text = tk.Text(text_frame, height=8, wrap="word")
        log_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志", command=self.clear_log).pack(pady=5)

    def refresh_station_list(self):
        """刷新车站列表"""
        # 清空现有项目
        for item in self.station_tree.get_children():
            self.station_tree.delete(item)

        # 添加车站
        for station in self.config_data["stations"]:
            asd_total = station["config"]["asd_count"]["sx"] + station["config"]["asd_count"]["xx"]
            status = "启用" if station["enabled"] else "禁用"

            self.station_tree.insert("", "end", values=(
                station["station_id"],
                station["station_name"],
                status,
                asd_total,
                "就绪"
            ))

    def add_station(self):
        """添加车站"""
        dialog = StationConfigDialog(self.root)
        self.root.wait_window(dialog.dialog)

        if dialog.result:
            self.config_data["stations"].append(dialog.result)
            self.refresh_station_list()
            self.log_message(f"添加车站: {dialog.result['station_id']} - {dialog.result['station_name']}")

    def edit_station(self):
        """编辑车站"""
        selection = self.station_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的车站")
            return

        item = selection[0]
        station_id = self.station_tree.item(item)["values"][0]

        # 找到对应的车站配置
        station_config = None
        station_index = None
        for i, station in enumerate(self.config_data["stations"]):
            if station["station_id"] == station_id:
                station_config = station
                station_index = i
                break

        if station_config:
            dialog = StationConfigDialog(self.root, station_config)
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                self.config_data["stations"][station_index] = dialog.result
                self.refresh_station_list()
                self.log_message(f"编辑车站: {dialog.result['station_id']} - {dialog.result['station_name']}")

    def delete_station(self):
        """删除车站"""
        selection = self.station_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的车站")
            return

        if messagebox.askyesno("确认", "确定要删除选中的车站吗？"):
            for item in selection:
                station_id = self.station_tree.item(item)["values"][0]
                self.config_data["stations"] = [s for s in self.config_data["stations"] if s["station_id"] != station_id]
                self.log_message(f"删除车站: {station_id}")

            self.refresh_station_list()

    def batch_enable(self):
        """批量启用车站"""
        selection = self.station_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要启用的车站")
            return

        for item in selection:
            station_id = self.station_tree.item(item)["values"][0]
            for station in self.config_data["stations"]:
                if station["station_id"] == station_id:
                    station["enabled"] = True
                    break

        self.refresh_station_list()
        self.log_message(f"批量启用 {len(selection)} 个车站")

    def batch_disable(self):
        """批量禁用车站"""
        selection = self.station_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要禁用的车站")
            return

        for item in selection:
            station_id = self.station_tree.item(item)["values"][0]
            for station in self.config_data["stations"]:
                if station["station_id"] == station_id:
                    station["enabled"] = False
                    break

        self.refresh_station_list()
        self.log_message(f"批量禁用 {len(selection)} 个车站")

    def configure_fields(self):
        """配置数据字段"""
        selection = self.station_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要配置字段的车站")
            return

        item = selection[0]
        station_id = self.station_tree.item(item)["values"][0]

        # 找到对应的车站配置
        station_config = None
        station_index = None
        for i, station in enumerate(self.config_data["stations"]):
            if station["station_id"] == station_id:
                station_config = station
                station_index = i
                break

        if station_config:
            from field_config_dialog import FieldConfigDialog
            dialog = FieldConfigDialog(self.root, station_config)
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                # 更新车站的字段配置
                self.config_data["stations"][station_index]["field_configs"] = dialog.result
                self.log_message(f"更新车站 {station_id} 的字段配置")
                messagebox.showinfo("成功", "字段配置已更新")

    def save_config(self):
        """保存配置到文件"""
        try:
            # 更新配置数据
            self.config_data["mqtt_config"]["server"] = self.server_var.get()
            self.config_data["mqtt_config"]["port"] = self.port_var.get()
            self.config_data["mqtt_config"]["username"] = self.username_var.get()
            self.config_data["mqtt_config"]["password"] = self.password_var.get()
            self.config_data["line_info"]["line_id"] = self.line_id_var.get()
            self.config_data["line_info"]["line_name"] = self.line_name_var.get()
            self.config_data["global_settings"]["loop_count"] = self.loop_count_var.get()
            self.config_data["global_settings"]["send_interval"] = self.interval_var.get()
            self.config_data["global_settings"]["concurrent_stations"] = self.concurrent_var.get()
            self.config_data["global_settings"]["show_data_content"] = self.show_data_content_var.get()
            self.config_data["global_settings"]["log_detail_level"] = self.log_detail_var.get()

            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="保存配置文件"
            )

            if filename:
                with open(filename, "w", encoding="utf-8") as f:
                    json.dump(self.config_data, f, ensure_ascii=False, indent=2)
                self.log_message(f"配置已保存到: {filename}")
                messagebox.showinfo("成功", "配置保存成功")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def load_config(self):
        """从文件加载配置"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="加载配置文件"
            )

            if filename:
                with open(filename, "r", encoding="utf-8") as f:
                    self.config_data = json.load(f)

                # 更新界面
                self.server_var.set(self.config_data["mqtt_config"]["server"])
                self.port_var.set(self.config_data["mqtt_config"]["port"])
                self.username_var.set(self.config_data["mqtt_config"]["username"])
                self.password_var.set(self.config_data["mqtt_config"]["password"])
                self.line_id_var.set(self.config_data["line_info"]["line_id"])
                self.line_name_var.set(self.config_data["line_info"]["line_name"])
                self.loop_count_var.set(self.config_data["global_settings"]["loop_count"])
                self.interval_var.set(self.config_data["global_settings"]["send_interval"])

                self.refresh_station_list()
                self.log_message(f"配置已从 {filename} 加载")
                messagebox.showinfo("成功", "配置加载成功")
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

    def log_data_content(self, station_id, topic, payload, current_loop):
        """记录发送的数据内容"""
        # 检查是否需要显示数据内容
        if not self.show_data_content_var.get():
            return

        # 获取设备类型
        topic_parts = topic.split("/")
        if len(topic_parts) >= 4:
            if len(topic_parts) == 4:
                device_type = topic_parts[3]
                device_name = device_type
            else:
                device_name = topic_parts[4]
                if device_name.startswith("ASD"):
                    device_type = "ASD"
                elif device_name.startswith("EED"):
                    device_type = "EED"
                elif device_name.startswith("MSD"):
                    device_type = "MSD"
                elif device_name.startswith("PDS"):
                    device_type = "PDS"
                elif device_name.startswith("PSL"):
                    device_type = "PSL"
                elif device_name == "IBP":
                    device_type = "IBP"
                elif device_name == "PEDC":
                    device_type = "PEDC"
                elif device_name == "SIG":
                    device_type = "SIG"
                else:
                    device_type = device_name
        else:
            device_type = "UNKNOWN"
            device_name = "UNKNOWN"

        # 过滤掉timestamp字段
        data_fields = {k: v for k, v in payload.items() if k != "timestamp"}

        # 根据详细级别格式化数据内容
        detail_level = self.log_detail_var.get()

        if detail_level == "简单":
            # 只显示设备名和字段总数
            fields_str = f"共{len(data_fields)}个字段"
        elif detail_level == "智能":
            # 智能显示重要字段
            important_fields = []
            other_fields = []

            for k, v in data_fields.items():
                # 识别重要字段（通常是状态、故障、开关量等）
                if any(keyword in k.upper() for keyword in ['HDM_', 'LCB_', 'YC_', 'GZ', 'BJ', 'STATUS', 'ALARM']):
                    if v != 0:  # 非零值更重要
                        important_fields.append(f"{k}={v}")
                    else:
                        other_fields.append(f"{k}={v}")
                else:
                    other_fields.append(f"{k}={v}")

            # 构建显示字符串
            display_parts = []

            # 优先显示重要的非零字段
            if important_fields:
                display_parts.extend(important_fields[:6])

            # 如果还有空间，显示其他字段
            remaining_space = 8 - len(display_parts)
            if remaining_space > 0 and other_fields:
                display_parts.extend(other_fields[:remaining_space])

            fields_str = ", ".join(display_parts)

            # 如果还有更多字段，显示总数
            total_shown = len(display_parts)
            if total_shown < len(data_fields):
                fields_str += f", ... (显示{total_shown}/{len(data_fields)}个字段)"
        elif detail_level == "详细":
            # 显示更多字段
            if len(data_fields) <= 15:
                fields_str = ", ".join([f"{k}={v}" for k, v in data_fields.items()])
            else:
                first_fields = list(data_fields.items())[:12]
                fields_str = ", ".join([f"{k}={v}" for k, v in first_fields])
                fields_str += f", ... (显示12/{len(data_fields)}个字段)"
        else:  # 完整
            # 显示所有字段
            fields_str = ", ".join([f"{k}={v}" for k, v in data_fields.items()])

        # 记录日志
        log_message = f"循环{current_loop} - {station_id}/{device_name}: {fields_str}"
        self.log_message(log_message)

    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)

    def start_sending(self):
        """开始发送数据"""
        try:
            # 检查是否有启用的车站
            enabled_stations = [s for s in self.config_data["stations"] if s["enabled"]]
            if not enabled_stations:
                messagebox.showwarning("警告", "没有启用的车站，请先添加并启用车站")
                return

            # 重置开关量字段状态
            from digital_field_generator import digital_generator
            digital_generator.reset_field_states()

            # 连接MQTT服务器
            self.mqtt_client = mqtt.Client()
            if self.username_var.get() and self.password_var.get():
                self.mqtt_client.username_pw_set(self.username_var.get(), self.password_var.get())

            self.mqtt_client.connect(self.server_var.get(), self.port_var.get())
            self.mqtt_client.loop_start()

            # 更新界面状态
            self.is_sending = True
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")

            # 启动发送线程
            self.sending_thread = threading.Thread(target=self.sending_worker, daemon=True)
            self.sending_thread.start()

            self.log_message(f"开始向 {len(enabled_stations)} 个车站发送数据")

        except Exception as e:
            messagebox.showerror("错误", f"启动发送失败: {e}")
            self.log_message(f"启动发送失败: {e}")

    def stop_sending(self):
        """停止发送数据"""
        self.is_sending = False

        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
            self.mqtt_client = None

        # 更新界面状态
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")

        self.log_message("数据发送已停止")

    def sending_worker(self):
        """数据发送工作线程（线程池并行版本）"""
        try:
            enabled_stations = [s for s in self.config_data["stations"] if s["enabled"]]
            line_id = self.line_id_var.get()
            loop_count = self.loop_count_var.get()
            send_interval = self.interval_var.get() / 1000.0  # 转换为秒
            max_workers = self.concurrent_var.get()

            self.log_message(f"开始并行发送，最大并发车站数: {max_workers}")

            for loop in range(loop_count):
                if not self.is_sending:
                    break

                start_time = time.time()

                # 使用线程池并行发送车站数据
                total_messages = self.send_loop_data_parallel(
                    enabled_stations, line_id, loop + 1, loop_count, max_workers
                )

                end_time = time.time()
                elapsed_time = end_time - start_time

                self.log_message(f"循环 {loop + 1}/{loop_count} 完成，发送 {total_messages} 条消息，耗时 {elapsed_time:.2f} 秒")

                # 等待间隔
                if loop < loop_count - 1 and self.is_sending:
                    time.sleep(send_interval)

            # 重置车站状态
            for station in enabled_stations:
                self.update_station_status(station["station_id"], "就绪")

            self.log_message("所有数据发送完成")

        except Exception as e:
            self.log_message(f"发送过程中出错: {e}")
        finally:
            # 确保界面状态正确
            self.root.after(0, self.stop_sending)

    def send_loop_data_parallel(self, enabled_stations, line_id, current_loop, total_loops, max_workers):
        """使用线程池并行发送单个循环的数据"""
        total_messages = 0

        # 使用线程池执行器
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有车站的发送任务
            future_to_station = {}
            for station_config in enabled_stations:
                if not self.is_sending:
                    break

                future = executor.submit(
                    self.send_station_data,
                    station_config, line_id, current_loop, total_loops
                )
                future_to_station[future] = station_config["station_id"]

            # 收集结果
            for future in as_completed(future_to_station):
                if not self.is_sending:
                    break

                station_id = future_to_station[future]
                try:
                    messages_sent = future.result(timeout=30)  # 30秒超时
                    total_messages += messages_sent
                    self.update_station_status(station_id, "完成")
                except Exception as e:
                    self.log_message(f"车站 {station_id} 发送失败: {e}")
                    self.update_station_status(station_id, "错误")

        return total_messages

    def send_station_data(self, station_config, line_id, current_loop, total_loops):
        """发送单个车站的数据"""
        station_id = station_config["station_id"]
        messages_sent = 0

        try:
            # 更新状态为发送中
            self.update_station_status(station_id, "发送中")

            # 生成车站数据
            messages = self.data_generator.generate_station_data(
                line_id, station_config, current_loop, total_loops
            )

            # 发送所有消息
            for msg in messages:
                if not self.is_sending:
                    break

                topic = msg["topic"]
                payload = json.dumps(msg["payload"])

                # 发送MQTT消息
                self.mqtt_client.publish(topic, payload)
                messages_sent += 1

                # 记录发送的数据内容
                self.log_data_content(station_id, topic, msg["payload"], current_loop)

            return messages_sent

        except Exception as e:
            self.log_message(f"车站 {station_id} 数据生成/发送失败: {e}")
            raise

    def update_station_status(self, station_id, status):
        """更新车站状态"""
        def update():
            for item in self.station_tree.get_children():
                if self.station_tree.item(item)["values"][0] == station_id:
                    values = list(self.station_tree.item(item)["values"])
                    values[4] = status  # 更新状态列
                    self.station_tree.item(item, values=values)
                    break

        self.root.after(0, update)


if __name__ == "__main__":
    print("正在启动多车站MQTT数据发生器...")
    try:
        root = tk.Tk()
        app = MultiStationMQTTGenerator(root)
        print("应用程序已初始化，启动GUI...")
        root.mainloop()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
