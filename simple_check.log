macOS 11 (1107) or later required, have instead 11 (1106) !
=== Simple System Check ===
Python version: 2.7.16 (default, Aug 29 2022, 10:34:32) 
[GCC Apple LLVM 12.0.5 (clang-1205.0.19.59.6) [+internal-os, ptrauth-isa=deploy
Python executable: /System/Library/Frameworks/Python.framework/Versions/2.7/Resources/Python.app/Contents/MacOS/Python
Working directory: /Users/<USER>/MySource/PSDMqttMsgSender
Platform: darwin

=== Basic Module Check ===
OK: json
OK: time
OK: threading
OK: logging
OK: random

=== GUI Check ===
OK: GUI module imported
