#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
System check script to verify the environment
"""
import sys
import os

def main():
    print("=== System Check ===")
    print("Python version:", sys.version)
    print("Python executable:", sys.executable)
    print("Working directory:", os.getcwd())
    print("Platform:", sys.platform)
    
    print("\n=== Module Check ===")
    
    # Check basic modules
    modules_to_check = [
        'json',
        'time',
        'threading',
        'logging',
        'random',
        'typing'
    ]
    
    for module in modules_to_check:
        try:
            __import__(module)
            print("OK:", module)
        except ImportError as e:
            print("ERROR:", module, "-", str(e))
    
    # Check paho-mqtt
    try:
        import paho.mqtt.client as mqtt
        print("OK: paho.mqtt.client")
    except ImportError as e:
        print("ERROR: paho.mqtt.client -", str(e))
    
    # Check local modules
    try:
        from data_generator import StationDataGenerator
        print("OK: data_generator")
    except ImportError as e:
        print("ERROR: data_generator -", str(e))
    
    try:
        from data_fields_config import DATA_FIELDS_CONFIG
        print("OK: data_fields_config")
    except ImportError as e:
        print("ERROR: data_fields_config -", str(e))
    
    # Check tkinter last (might have issues)
    print("\n=== GUI Check ===")
    try:
        import tkinter as tk
        print("OK: tkinter imported")
        
        # Try to create a simple window
        root = tk.Tk()
        root.withdraw()  # Hide the window
        print("OK: tkinter window created")
        root.destroy()
        print("OK: tkinter window destroyed")
        
    except Exception as e:
        print("ERROR: tkinter -", str(e))
    
    print("\n=== File Check ===")
    files_to_check = [
        'multi_station_gui.py',
        'data_generator.py',
        'data_fields_config.py',
        'digital_field_generator.py',
        'field_config_dialog.py'
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print("OK:", filename, "exists")
        else:
            print("ERROR:", filename, "not found")
    
    print("\n=== System Check Complete ===")

if __name__ == "__main__":
    main()
