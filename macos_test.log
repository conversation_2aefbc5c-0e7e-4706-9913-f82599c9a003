error: can't create or remove files in install directory

The following error occurred while trying to add or remove files in the
installation directory:

    [Errno 13] Permission denied: '/Library/Python/2.7/site-packages/test-easy-install-10781.write-test'

The installation directory you specified (via --install-dir, --prefix, or
the distutils default setting) was:

    /Library/Python/2.7/site-packages/

Perhaps your account does not have write access to this directory?  If the
installation directory is a system-owned directory, you may need to sign in
as the administrator or "root" account.  If you do not have administrative
access to this machine, you may wish to choose a different installation
directory, preferably one that is listed in your PYTHONPATH environment
variable.

For information on other options, you may wish to consult the
documentation at:

  https://setuptools.readthedocs.io/en/latest/easy_install.html

Please make the appropriate changes for your system and try again.

macOS 11 (1107) or later required, have instead 11 (1106) !
=== macOS MQTT数据发生器启动器 ===
当前Python版本: 2.7.16
✓ GUI库可用
✗ MQTT库不可用
发现缺失依赖: paho-mqtt
正在安装paho-mqtt...
✗ paho-mqtt安装失败: Command '['easy_install', 'paho-mqtt']' returned non-zero exit status 1
依赖安装失败，将使用简化版本
正在启动GUI程序...
注意: Python 2.7支持有限，建议升级到Python 3
尝试运行基础版本...
