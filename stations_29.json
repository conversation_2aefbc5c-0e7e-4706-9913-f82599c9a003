{"line_info": {"line_id": "L03", "line_name": "3号线", "description": "地铁3号线站台门模拟数据"}, "mqtt_config": {"server": "*************", "port": 1883, "username": "metroit", "password": "dtdkws", "qos": 0, "retain": false}, "global_settings": {"send_interval": 15000, "loop_count": 1, "concurrent_stations": 5, "data_variation": 0.1}, "stations": [{"station_id": "S18", "station_name": "大柏树车站", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "field_configs": {"POWER": {"SQZDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SQBYDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SD_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XDC_WD_GG_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PS": {"XDC_BM_WD": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_NZ": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "ASD": {"LCB_GL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_ZD": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_JDKM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_JDGM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_ZKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_ZGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DWGL_ZT": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SDJS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZAWTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCSJSYC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCU_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "GM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_DL": {"range": [0, 100], "fixed_value": "", "enabled": false}, "DJ_SD": {"range": [0, 100], "fixed_value": "", "enabled": false}, "DJ_SCNJ": {"range": [0, 100], "fixed_value": "", "enabled": false}, "QDB_DY": {"range": [0, 100], "fixed_value": "", "enabled": false}, "YC_KM_ML_DY": {"range": [0, 100], "fixed_value": "", "enabled": false}, "YC_GM_ML_DY": {"range": [0, 100], "fixed_value": "", "enabled": false}}, "EED": {"YJM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YJM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YJM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "MSD": {"CT_DM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_DK_CS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_DK_CS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "IBP": {"IBP_SNXX": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_SQ_PSL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_KM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_GM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PDS": {"JXTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PEDC": {"JKXT_PEDC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XCZX_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZC_HDM_GMSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_YJM_GBSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_JXTC_XT_AQ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}, "GM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "PSL": {"PSL_SN": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_HSJC": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "SIG": {"XH_KM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XH_GM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZW_XH": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HS_JC": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "GB_SJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSD_GD_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "ZW_XH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "KM_ML_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "GM_ML_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "SIG_GD_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "ZCGB_SJXH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "HSJCXH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}}}}, {"station_id": "S02", "station_name": "车站1", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}, "field_configs": {"POWER": {"SQZDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SQBYDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SD_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XDC_WD_GG_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PS": {"XDC_BM_WD": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_NZ": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "ASD": {"LCB_GL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_ZD": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_JDKM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "LCB_JDGM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_ZKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_ZGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DWGL_ZT": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SDJS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZAWTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCSJSYC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCU_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "GM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_DL": {"range": [0, 0], "fixed_value": "0", "enabled": true}, "DJ_SD": {"range": [0, 0], "fixed_value": "0", "enabled": true}, "DJ_SCNJ": {"range": [0, 0], "fixed_value": "0", "enabled": true}, "QDB_DY": {"range": [0, 0], "fixed_value": "0", "enabled": true}, "YC_KM_ML_DY": {"range": [0, 0], "fixed_value": "0", "enabled": true}, "YC_GM_ML_DY": {"range": [0, 0], "fixed_value": "0", "enabled": true}}, "EED": {"YJM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YJM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YJM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "MSD": {"CT_DM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_DK_CS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CT_DM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_DK": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_GB": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_DK_CS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "CW_DM_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "IBP": {"IBP_SNXX": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_SQ_PSL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_KM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_GM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PDS": {"JXTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PEDC": {"JKXT_PEDC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XCZX_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZC_HDM_GMSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_YJM_GBSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_JXTC_XT_AQ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}, "GM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "PSL": {"PSL_SN": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_HSJC": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "SIG": {"XH_KM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XH_GM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZW_XH": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HS_JC": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "GB_SJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSD_GD_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "ZW_XH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "KM_ML_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "GM_ML_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "SIG_GD_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "ZCGB_SJXH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}, "HSJCXH_DY": {"range": [58, 58], "fixed_value": "", "enabled": true}}}}, {"station_id": "S01", "station_name": "上海南站站", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}, "field_configs": {"POWER": {"SQZDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SQBYDY_TR": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SD_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDY_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_TR_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "QDDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KZDC_DY_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XDC_WD_GG_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL1_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL2_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL3_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL4_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_QDDY_HL5_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_SIG_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PSL_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_IBP_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_PEDC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DCU_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_AQ_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_JXTC_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_DKD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XX_KZDY_LWD_HL_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PS": {"XDC_BM_WD": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_NZ": {"range": [0, 100], "fixed_value": "", "enabled": true}, "XDC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KZDY_SC_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "ASD": {"LCB_GL": {"default_value": 0, "trigger_loop": 1, "trigger_mode": "toggle", "fixed_value": "", "enabled": true}, "LCB_ZD": {"default_value": 0, "trigger_loop": 1, "trigger_mode": "toggle", "fixed_value": "", "enabled": true}, "LCB_JDKM": {"default_value": 0, "trigger_loop": 1, "trigger_mode": "toggle", "fixed_value": "", "enabled": true}, "LCB_JDGM": {"default_value": 0, "trigger_loop": 1, "trigger_mode": "toggle", "fixed_value": "", "enabled": true}, "HDM_ZKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YKDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_ZGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "HDM_YGDW": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YC_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DWGL_ZT": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SDJS_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZAWTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCSJSYC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DCU_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "YXWKG_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "GM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "KM_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "DJ_DL": {"range": [0, 100], "fixed_value": "", "enabled": true}, "DJ_SD": {"range": [0, 100], "fixed_value": "", "enabled": true}, "DJ_SCNJ": {"range": [0, 100], "fixed_value": "", "enabled": true}, "QDB_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "YC_KM_ML_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "YC_GM_ML_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "EED": {"EED_STATUS": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "EED_TEMPERATURE": {"range": [0, 100], "fixed_value": "", "enabled": true}, "EED_VOLTAGE": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "MSD": {"MSD_STATUS": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "MSD_PRESSURE": {"range": [0, 100], "fixed_value": "", "enabled": true}, "MSD_FLOW": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "IBP": {"IBP_SNXX": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_SQ_PSL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_KM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "IBP_GM_ML": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PDS": {"JXTC_BJ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_PL": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "JXTC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "PEDC": {"JKXT_PEDC_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "XCZX_GZ": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "ZC_HDM_GMSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_YJM_GBSJ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "ZC_JXTC_XT_AQ_XH_DY": {"range": [0, 100], "fixed_value": "", "enabled": true}, "KM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}, "GM_ML_SCJDQ_JC": {"range": [0, 100], "fixed_value": "", "enabled": true}}, "PSL": {"PSL_SN": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_KM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_GM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "PSL_HSJC": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}, "SIG": {"SIG_STATUS": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}, "SIG_ALARM": {"default_value": 0, "trigger_loop": 0, "trigger_mode": "once", "fixed_value": "", "enabled": true}}}}, {"station_id": "S03", "station_name": "车站3", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S04", "station_name": "车站4", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S05", "station_name": "车站5", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S06", "station_name": "车站6", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S07", "station_name": "车站5", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S08", "station_name": "车站8", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S09", "station_name": "车站9", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S10", "station_name": "车站10", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S11", "station_name": "车站11", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S12", "station_name": "车站12", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S13", "station_name": "车站13", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S14", "station_name": "车站14", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S15", "station_name": "车站15", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S16", "station_name": "车站16", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S17", "station_name": "车站17", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S19", "station_name": "车站19", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S20", "station_name": "车站20", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S21", "station_name": "车站21", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S22", "station_name": "车站22", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S23", "station_name": "车站23", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S24", "station_name": "车站24", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S25", "station_name": "车站25", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S26", "station_name": "车站26", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S27", "station_name": "车站27", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S28", "station_name": "车站28", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}, {"station_id": "S29", "station_name": "车站29", "enabled": true, "config": {"asd_count": {"sx": 30, "xx": 30}, "eed_count": {"sx": 8, "xx": 8}, "msd_count": {"sx": 2, "xx": 2}, "pds_count": {"sx": 8, "xx": 8}, "psl_count": {"sx": 3, "xx": 3}}, "data_ranges": {"power_data": {"digital_signals": [0, 1], "analog_signals": [0, 100]}, "ps_data": {"temperature": [0, 100], "voltage": [0, 100], "current": [0, 100]}, "asd_data": {"digital_signals": [0, 1], "motor_current": [0, 100], "motor_speed": [0, 100], "motor_torque": [0, 100], "door_voltage": [0, 100], "control_voltage": [0, 100]}, "eed_data": {"status": [0, 1], "temperature": [0, 100], "voltage": [0, 100]}, "msd_data": {"status": [0, 1], "pressure": [0, 100], "flow": [0, 100]}}}]}