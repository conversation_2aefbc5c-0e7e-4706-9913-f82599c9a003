macOS 11 (1107) or later required, have instead 11 (1106) !
=== Multi-Station MQTT Data Generator (Python 2.7) ===
Python version: 2.7.16 (default, Aug 29 2022, 10:34:32) 
[GCC Apple LLVM 12.0.5 (clang-1205.0.19.59.6) [+internal-os, ptrauth-isa=deploy
Working directory: /Users/<USER>/MySource/PSDMqttMsgSender
1. Checking dependencies...
   OK: Tkinter available
   WARNING: paho-mqtt not available: No module named paho.mqtt.client
   (This is OK for GUI testing, but needed for MQTT functionality)
2. Note: The original GUI is designed for Python 3
   It uses features not available in Python 2.7
   You may need to:
   - Update to a newer macOS version
   - Install Python 3.9+ with proper macOS support
   - Or modify the code for Python 2.7 compatibility
3. Creating a simple test window...
