#!/usr/bin/env python3
import tkinter as tk
import sys

print("开始创建GUI窗口...")

try:
    # 创建主窗口
    root = tk.Tk()
    root.title("MQTT数据发生器测试")
    root.geometry("400x300")
    
    # 强制窗口显示在前台
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    print("窗口已创建，应该可以看到了")
    
    # 添加一些基本控件
    label = tk.Label(root, text="如果您能看到这个窗口，说明GUI正常工作", font=("Arial", 12))
    label.pack(pady=20)
    
    button = tk.Button(root, text="关闭", command=root.quit)
    button.pack(pady=10)
    
    print("开始主循环...")
    root.mainloop()
    print("程序结束")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
