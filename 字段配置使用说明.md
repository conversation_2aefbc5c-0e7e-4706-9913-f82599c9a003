# 数据字段详细配置功能说明

## 概述

新版本的多车站MQTT数据发生器现在支持对每个数据字段进行详细配置，包括：
- **开关量字段**：可以设置为固定的0或1，或者在0-1之间随机变化
- **数字量字段**：可以设置数值范围，或者固定值
- **字段启用/禁用**：可以选择性地启用或禁用某些字段

## 支持的数据类型和字段

### 1. POWER - 电源数据
包含39个字段，全部为开关量（0/1）：
- `SQZDY_TR` - 上桥整流器投入
- `SQBYDY_TR` - 上桥备用电源投入
- `SD_GZ` - 手动故障
- `QDDY_GZ` - 强电电源故障
- `KZDY_GZ` - 控制电源故障
- 以及其他34个电源相关的状态和报警字段

### 2. PS - 传感器数据
包含7个数字量字段（默认范围0-100）：
- `XDC_BM_WD` - 箱体表面温度
- `XDC_NZ` - 箱体内阻
- `XDC_DY` - 箱体电压
- `QDDY_SC_DL` - 强电电源输出电流
- `KZDY_SC_DL` - 控制电源输出电流
- `QDDY_SC_DY` - 强电电源输出电压
- `KZDY_SC_DY` - 控制电源输出电压

### 3. ASD - 自动滑动门数据
包含26个字段：
- **20个开关量字段**：各种状态和故障信号
- **6个数字量字段**：电机电流、速度、扭矩、电压等

### 4. EED - 紧急疏散门数据
包含3个字段：
- `EED_STATUS` - 状态（开关量）
- `EED_TEMPERATURE` - 温度（数字量）
- `EED_VOLTAGE` - 电压（数字量）

### 5. MSD - 手动滑动门数据
包含3个字段：
- `MSD_STATUS` - 状态（开关量）
- `MSD_PRESSURE` - 压力（数字量）
- `MSD_FLOW` - 流量（数字量）

### 6. IBP - 综合后备盘数据
包含4个开关量字段：
- `IBP_SNXX` - 上下行信息
- `IBP_SQ_PSL` - 授权PSL
- `IBP_KM_ML` - 开门脉冲
- `IBP_GM_ML` - 关门脉冲

### 7. PDS - 站台门检测数据
包含3个开关量字段：
- `JXTC_BJ` - 检修通道报警
- `JXTC_PL` - 检修通道屏蔽
- `JXTC_GZ` - 检修通道故障

### 8. PEDC - 站台门控制数据
包含7个字段：
- **2个开关量字段**：系统故障状态
- **5个数字量字段**：各种信号电压和检测值

### 9. PSL - 站台安全线数据
包含4个开关量字段：
- `PSL_SN` - 上下行
- `PSL_KM` - 开门
- `PSL_GM` - 关门
- `PSL_HSJC` - 回送检测

### 10. SIG - 信号数据
包含2个开关量字段：
- `SIG_STATUS` - 信号状态
- `SIG_ALARM` - 信号报警

## 如何使用字段配置功能

### 1. 打开字段配置对话框
有两种方式：
- **方式一**：在车站列表中选择车站，点击"字段配置"按钮
- **方式二**：在车站配置对话框中，点击"字段配置"按钮

### 2. 配置界面说明
字段配置对话框采用标签页设计，每个数据类型一个标签页：

#### 标签页内容：
- **数据类型信息**：显示类型名称、描述、主题后缀等
- **字段配置表格**：
  - **字段名称**：数据字段的名称
  - **描述**：字段的中文描述
  - **类型**：开关量或数字量
  - **最小值**：数值范围的最小值
  - **最大值**：数值范围的最大值
  - **固定值**：如果设置，将使用固定值而不是随机值
  - **启用**：是否启用该字段

### 3. 配置选项详解

#### 开关量字段配置：
- **默认值**：字段的默认值，可以是0或1
- **触发循环**：在第几次循环后触发（设置为1的条件）
- **触发模式**：
  - `once` - 单次触发：仅在指定循环触发一次
  - `toggle` - 切换模式：从指定循环开始每次切换状态
  - `random` - 随机模式：从指定循环开始随机输出0/1
  - `持续` - 持续模式：从指定循环开始持续输出1
- **固定值**：可以设置为0或1，表示该字段始终输出固定值（优先级最高）
- **启用**：取消勾选则该字段输出0

#### 数字量字段配置：
- **最小值/最大值**：设置数值范围，程序将在此范围内生成随机数
- **固定值**：设置固定数值，该字段将始终输出此值
- **启用**：取消勾选则该字段输出0

### 4. 配置管理功能

#### 重置为默认：
- 将所有字段恢复为默认配置
- 开关量字段默认范围为[0,1]
- 数字量字段默认范围为[0,100]

#### 导入配置：
- 从JSON文件导入字段配置
- 可以在不同车站之间复制配置

#### 导出配置：
- 将当前字段配置导出为JSON文件
- 便于备份和分享配置

## 配置示例

### 示例1：模拟故障场景
如果要模拟某个车站在第3次循环时发生电源故障：
1. 打开POWER标签页
2. 找到`QDDY_GZ`（强电电源故障）字段
3. 设置默认值为0，触发循环为3，触发模式为"持续"
4. 这样在前2次循环该字段为0，从第3次循环开始持续为1

### 示例2：模拟温度异常
如果要模拟温度传感器异常：
1. 打开PS标签页
2. 找到`XDC_BM_WD`（箱体表面温度）字段
3. 设置范围为[80, 100]模拟高温
4. 或设置固定值为95模拟持续高温

### 示例3：禁用某些设备
如果某个车站没有某些设备：
1. 找到对应的数据类型标签页
2. 取消勾选不需要的字段的"启用"选项
3. 这些字段将输出0，表示设备不存在

## 配置文件格式

字段配置以JSON格式存储在车站配置中：

```json
{
  "station_id": "S01",
  "station_name": "车站01",
  "field_configs": {
    "POWER": {
      "SQZDY_TR": {
        "range": [0, 1],
        "fixed_value": "",
        "enabled": true
      },
      "QDDY_GZ": {
        "range": [0, 1],
        "fixed_value": "1",
        "enabled": true
      }
    },
    "PS": {
      "XDC_BM_WD": {
        "range": [20, 80],
        "fixed_value": "",
        "enabled": true
      }
    }
  }
}
```

## 注意事项

1. **配置优先级**：固定值 > 启用状态 > 范围配置
2. **数据类型**：确保固定值符合字段的数据类型要求
3. **范围验证**：最小值不能大于最大值
4. **性能影响**：大量字段配置可能略微影响数据生成性能
5. **配置保存**：字段配置会自动保存到车站配置中

## 高级用法

### 批量配置多个车站
1. 配置一个车站的字段
2. 导出字段配置为JSON文件
3. 为其他车站导入相同的配置

### 场景化配置
可以为不同的测试场景创建不同的配置文件：
- `正常运行.json` - 所有字段正常范围
- `故障模拟.json` - 包含各种故障状态
- `极端环境.json` - 模拟极端温度、电压等

这样可以快速切换不同的测试场景，提高测试效率。
