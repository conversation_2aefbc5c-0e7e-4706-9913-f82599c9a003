"""
测试修复后的toggle逻辑
"""
from digital_field_generator import digital_generator

def test_toggle_fix():
    """测试修复后的toggle逻辑"""
    print("=== Toggle修复测试 ===\n")
    
    # 重置状态
    digital_generator.reset_field_states()
    
    # 测试HDM_YGDW的实际配置
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("配置: 默认值=0, 触发循环=2, 模式=toggle")
    print("预期: 循环1=0, 循环2=1, 循环3=0, 循环4=1, ...")
    print()
    
    field_key = "HDM_YGDW_test"
    results = []
    
    print("实际结果:")
    for loop in range(1, 11):
        value = digital_generator.generate_digital_value(
            field_key,
            config,
            loop,
            10
        )
        results.append(str(value))
        print(f"循环 {loop:2d}: {value}")
    
    print(f"\n完整序列: {' -> '.join(results)}")
    
    # 验证结果
    expected = ['0', '1', '0', '1', '0', '1', '0', '1', '0', '1']
    if results == expected:
        print("✅ Toggle逻辑修复成功！")
    else:
        print("❌ Toggle逻辑仍有问题")
        print(f"预期: {' -> '.join(expected)}")
        print(f"实际: {' -> '.join(results)}")

def test_different_scenarios():
    """测试不同的toggle场景"""
    print("\n=== 不同场景测试 ===\n")
    
    scenarios = [
        {
            'name': '默认值0，第1次循环开始',
            'config': {'default_value': 0, 'trigger_loop': 1, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': ['1', '0', '1', '0', '1']
        },
        {
            'name': '默认值1，第2次循环开始',
            'config': {'default_value': 1, 'trigger_loop': 2, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': ['1', '0', '1', '0', '1']
        },
        {
            'name': '默认值0，第3次循环开始',
            'config': {'default_value': 0, 'trigger_loop': 3, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
            'expected': ['0', '0', '1', '0', '1']
        }
    ]
    
    for i, scenario in enumerate(scenarios):
        print(f"{i+1}. {scenario['name']}")
        
        # 重置状态
        digital_generator.reset_field_states()
        
        field_key = f"test_field_{i}"
        results = []
        
        for loop in range(1, 6):
            value = digital_generator.generate_digital_value(
                field_key,
                scenario['config'],
                loop,
                5
            )
            results.append(str(value))
        
        print(f"   预期: {' -> '.join(scenario['expected'])}")
        print(f"   实际: {' -> '.join(results)}")
        
        if results == scenario['expected']:
            print("   ✅ 正确")
        else:
            print("   ❌ 错误")
        print()

def test_multiple_fields():
    """测试多个字段的独立状态"""
    print("=== 多字段独立状态测试 ===\n")
    
    # 重置状态
    digital_generator.reset_field_states()
    
    fields = {
        'HDM_YGDW': {'default_value': 0, 'trigger_loop': 2, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
        'HDM_ZKDW': {'default_value': 1, 'trigger_loop': 1, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''},
        'YC_KM': {'default_value': 0, 'trigger_loop': 3, 'trigger_mode': 'toggle', 'enabled': True, 'fixed_value': ''}
    }
    
    print("字段配置:")
    for field_name, config in fields.items():
        print(f"  {field_name}: 默认值={config['default_value']}, 触发循环={config['trigger_loop']}")
    print()
    
    print("循环结果:")
    print("循环  HDM_YGDW  HDM_ZKDW  YC_KM")
    
    for loop in range(1, 8):
        values = {}
        for field_name, config in fields.items():
            value = digital_generator.generate_digital_value(
                field_name,
                config,
                loop,
                7
            )
            values[field_name] = value
        
        print(f"  {loop}      {values['HDM_YGDW']}        {values['HDM_ZKDW']}        {values['YC_KM']}")

if __name__ == "__main__":
    test_toggle_fix()
    test_different_scenarios()
    test_multiple_fields()
    
    print("\n=== 修复说明 ===")
    print("修复了toggle模式的bug:")
    print("- 之前：每次都重新初始化状态，导致除第一次外都是1")
    print("- 现在：只在第一次触发时初始化，后续正确切换状态")
    print("- 结果：现在能正确地在0和1之间切换")
