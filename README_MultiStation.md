# 多车站MQTT数据发生器

## 概述

这是一个重新设计的站台门模拟数据发生器，支持多个车站的配置和数据发送。相比原来的单车站版本，新版本提供了更强大的功能和更好的用户体验。

## 主要特性

### 🚇 多车站支持
- 支持同时配置和管理多个车站
- 每个车站可以独立配置设备数量和数据范围
- 支持批量启用/禁用车站

### 🔧 灵活配置
- 可视化的车站配置界面
- 支持不同车站的设备数量差异化配置
- 数据范围可自定义

### 📁 配置管理
- 配置文件的保存和加载
- 支持CSV格式的批量导入/导出
- 自动配置备份功能

### 🚀 高效发送
- 多线程并发数据发送
- 实时发送状态监控
- 详细的发送日志

## 文件结构

```
PSDMqttMsgSender/
├── multi_station_gui.py      # 主GUI应用程序
├── data_generator.py         # 数据生成器模块
├── config_manager.py         # 配置管理器
├── station_config.json       # 默认配置文件
├── main.py                   # 原单车站版本（保留）
└── README_MultiStation.md    # 本说明文档
```

## 使用方法

### 1. 启动应用
```bash
python multi_station_gui.py
```

### 2. 配置MQTT服务器
在左侧配置面板中设置：
- 服务器地址
- 端口号
- 用户名和密码（可选）

### 3. 配置线路信息
- 线路编号（如：L03）
- 线路名称（如：3号线）

### 4. 管理车站
- **添加车站**：点击"添加车站"按钮，配置车站信息和设备数量
- **编辑车站**：双击车站列表中的项目或选中后点击"编辑车站"
- **删除车站**：选中车站后点击"删除车站"
- **批量操作**：选中多个车站后可批量启用或禁用

### 5. 车站配置选项
每个车站可以配置以下设备数量：
- **ASD设备**：自动滑动门控制器（上行/下行方向）
- **EED设备**：紧急疏散门控制器（上行/下行方向）
- **MSD设备**：手动滑动门控制器（上行/下行方向）
- **PDS设备**：站台门检测系统（上行/下行方向）
- **PSL设备**：站台安全线系统（上行/下行方向）

### 6. 发送数据
- 设置循环次数和发送间隔
- 点击"开始发送"开始向所有启用的车站发送数据
- 实时查看发送日志和车站状态

### 7. 配置文件管理
- **保存配置**：将当前配置保存到JSON文件
- **加载配置**：从JSON文件加载配置
- **CSV导出**：将车站配置导出为CSV格式
- **CSV导入**：从CSV文件批量导入车站配置

## 数据格式

### MQTT主题格式
```
{线路编号}/{车站编号}/PSD/{设备类型}[/{方向}[/{设备编号}]]
```

示例：
- `L03/S01/PSD/POWER` - 电源数据
- `L03/S01/PSD/PS` - 传感器数据
- `L03/S01/PSD/SX/ASD01` - 上行方向第1个ASD设备
- `L03/S01/PSD/XX/EED01` - 下行方向第1个EED设备

### 数据类型
1. **POWER** - 电源状态数据
2. **PS** - 传感器数据（温度、电压、电流等）
3. **ASD** - 自动滑动门数据
4. **EED** - 紧急疏散门数据
5. **MSD** - 手动滑动门数据
6. **IBP** - 综合后备盘数据
7. **PDS** - 站台门检测数据
8. **PEDC** - 站台门控制数据
9. **PSL** - 站台安全线数据
10. **SIG** - 信号数据

## 配置文件格式

配置文件采用JSON格式，包含以下主要部分：

```json
{
  "line_info": {
    "line_id": "L03",
    "line_name": "3号线"
  },
  "mqtt_config": {
    "server": "49.235.38.216",
    "port": 1883,
    "username": "",
    "password": ""
  },
  "global_settings": {
    "send_interval": 1000,
    "loop_count": 1,
    "concurrent_stations": 5
  },
  "stations": [
    {
      "station_id": "S01",
      "station_name": "车站01",
      "enabled": true,
      "config": {
        "asd_count": {"sx": 30, "xx": 30},
        "eed_count": {"sx": 8, "xx": 8},
        "msd_count": {"sx": 2, "xx": 2},
        "pds_count": {"sx": 8, "xx": 8},
        "psl_count": {"sx": 3, "xx": 3}
      },
      "data_ranges": {
        // 数据范围配置...
      }
    }
  ]
}
```

## 性能优化

- 使用多线程并发发送数据
- 支持配置并发车站数量限制
- 内存高效的数据生成算法
- 实时状态更新机制

## 故障排除

### 常见问题

1. **无法连接MQTT服务器**
   - 检查服务器地址和端口是否正确
   - 确认网络连接正常
   - 验证用户名和密码（如果需要）

2. **数据发送失败**
   - 检查车站配置是否正确
   - 确认至少有一个车站被启用
   - 查看日志中的错误信息

3. **配置文件加载失败**
   - 检查JSON格式是否正确
   - 确认文件路径存在
   - 验证配置文件结构完整性

## 与原版本的区别

| 功能 | 原版本 | 新版本 |
|------|--------|--------|
| 车站数量 | 单车站 | 多车站 |
| 配置管理 | 手动输入 | 可视化配置 + 文件管理 |
| 数据发送 | 串行发送 | 并发发送 |
| 状态监控 | 基本日志 | 实时状态 + 详细日志 |
| 扩展性 | 有限 | 高度可扩展 |

## 技术架构

- **GUI框架**：tkinter + ttk
- **MQTT客户端**：paho-mqtt
- **数据生成**：模板化设计
- **配置管理**：JSON + CSV支持
- **并发处理**：threading模块

## 未来扩展

- 支持更多设备类型
- 数据可视化图表
- 历史数据记录
- 远程配置管理
- 集群部署支持
