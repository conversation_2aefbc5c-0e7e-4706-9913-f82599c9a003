#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
macOS兼容的启动器
自动检测环境并选择最佳运行方式
"""
import sys
import os
import subprocess

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print("当前Python版本: {}.{}.{}".format(version.major, version.minor, version.micro))
    return version

def check_dependencies():
    """检查依赖"""
    missing_deps = []
    
    # 检查tkinter
    try:
        if sys.version_info[0] >= 3:
            import tkinter
        else:
            import Tkinter
        print("✓ GUI库可用")
    except ImportError:
        print("✗ GUI库不可用")
        missing_deps.append("tkinter")
    
    # 检查paho-mqtt
    try:
        import paho.mqtt.client
        print("✓ MQTT库可用")
    except ImportError:
        print("✗ MQTT库不可用")
        missing_deps.append("paho-mqtt")
    
    return missing_deps

def install_dependencies(missing_deps):
    """安装缺失的依赖"""
    if "paho-mqtt" in missing_deps:
        print("正在安装paho-mqtt...")
        try:
            if sys.version_info[0] >= 3:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "paho-mqtt"])
            else:
                # Python 2.7 - 尝试不同的安装方法
                try:
                    subprocess.check_call(["pip", "install", "paho-mqtt"])
                except:
                    subprocess.check_call(["easy_install", "paho-mqtt"])
            print("✓ paho-mqtt安装成功")
            return True
        except Exception as e:
            print("✗ paho-mqtt安装失败: {}".format(str(e)))
            return False
    return True

def run_gui_safe():
    """安全地运行GUI程序"""
    try:
        print("正在启动GUI程序...")
        
        # 设置环境变量以避免某些macOS问题
        os.environ['PYTHONPATH'] = os.getcwd()
        
        if sys.version_info[0] >= 3:
            # Python 3版本
            exec(open('multi_station_gui.py').read())
        else:
            # Python 2版本 - 需要一些修改
            print("注意: Python 2.7支持有限，建议升级到Python 3")
            print("尝试运行基础版本...")
            create_simple_gui()
            
    except Exception as e:
        print("GUI启动失败: {}".format(str(e)))
        print("尝试创建简化版本...")
        create_simple_gui()

def create_simple_gui():
    """创建简化的GUI界面"""
    try:
        if sys.version_info[0] >= 3:
            import tkinter as tk
            from tkinter import ttk, messagebox
        else:
            import Tkinter as tk
            import ttk
            import tkMessageBox as messagebox
        
        root = tk.Tk()
        root.title("MQTT数据发生器 - 简化版")
        root.geometry("600x400")
        
        # 主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="多车站MQTT数据发生器", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 状态信息
        info_text = tk.Text(main_frame, height=15, width=70)
        info_text.pack(pady=10, fill="both", expand=True)
        
        # 添加系统信息
        info_text.insert(tk.END, "=== 系统信息 ===\n")
        info_text.insert(tk.END, "Python版本: {}\n".format(sys.version))
        info_text.insert(tk.END, "操作系统: {}\n".format(sys.platform))
        info_text.insert(tk.END, "工作目录: {}\n\n".format(os.getcwd()))
        
        info_text.insert(tk.END, "=== 程序状态 ===\n")
        info_text.insert(tk.END, "✓ GUI界面正常运行\n")
        info_text.insert(tk.END, "✓ 基础功能可用\n\n")
        
        info_text.insert(tk.END, "=== 使用说明 ===\n")
        info_text.insert(tk.END, "1. 当前运行的是简化版本\n")
        info_text.insert(tk.END, "2. 完整功能需要Python 3和所有依赖\n")
        info_text.insert(tk.END, "3. 建议升级macOS到12+版本\n")
        info_text.insert(tk.END, "4. 然后使用Python 3.9+运行完整程序\n\n")
        
        if os.path.exists("multi_station_gui.py"):
            info_text.insert(tk.END, "✓ 主程序文件存在\n")
        if os.path.exists("data_generator.py"):
            info_text.insert(tk.END, "✓ 数据生成器存在\n")
        if os.path.exists("data_fields_config.py"):
            info_text.insert(tk.END, "✓ 配置文件存在\n")
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        def try_full_version():
            messagebox.showinfo("提示", "正在尝试启动完整版本...")
            try:
                subprocess.Popen([sys.executable, "multi_station_gui.py"])
            except Exception as e:
                messagebox.showerror("错误", "启动失败: {}".format(str(e)))
        
        def close_app():
            root.destroy()
        
        ttk.Button(button_frame, text="尝试完整版本", command=try_full_version).pack(side="left", padx=5)
        ttk.Button(button_frame, text="关闭", command=close_app).pack(side="left", padx=5)
        
        print("简化GUI已启动，请查看窗口")
        root.mainloop()
        
    except Exception as e:
        print("简化GUI也无法启动: {}".format(str(e)))

def main():
    print("=== macOS MQTT数据发生器启动器 ===")
    
    # 检查Python版本
    version = check_python_version()
    
    # 检查依赖
    missing_deps = check_dependencies()
    
    # 安装缺失依赖
    if missing_deps:
        print("发现缺失依赖: {}".format(", ".join(missing_deps)))
        if not install_dependencies(missing_deps):
            print("依赖安装失败，将使用简化版本")
    
    # 运行程序
    run_gui_safe()

if __name__ == "__main__":
    main()
