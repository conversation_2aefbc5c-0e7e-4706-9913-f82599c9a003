# 开关量字段配置详细说明

## 概述

新版本的开关量配置采用了基于循环次数的触发机制，更符合实际的工业控制逻辑。每个开关量字段可以配置：
- **默认值**：字段的初始状态（0或1）
- **触发循环**：在第几次循环后改变状态
- **触发模式**：状态改变的方式
- **固定值**：强制设置固定输出值
- **启用状态**：是否启用该字段

## 触发模式详解

### 1. once - 单次触发
- **行为**：仅在指定循环触发一次，然后恢复默认值
- **适用场景**：模拟瞬时事件，如按钮按下、瞬时报警等
- **示例**：
  ```
  默认值: 0, 触发循环: 3, 模式: once
  循环1: 0 -> 循环2: 0 -> 循环3: 1 -> 循环4: 0 -> 循环5: 0
  ```

### 2. 持续 - 持续模式
- **行为**：从指定循环开始持续输出1
- **适用场景**：模拟故障状态、持续报警等
- **示例**：
  ```
  默认值: 0, 触发循环: 2, 模式: 持续
  循环1: 0 -> 循环2: 1 -> 循环3: 1 -> 循环4: 1 -> 循环5: 1
  ```

### 3. toggle - 切换模式
- **行为**：从指定循环开始每次循环切换状态
- **适用场景**：模拟开关动作、交替状态等
- **示例**：
  ```
  默认值: 0, 触发循环: 2, 模式: toggle
  循环1: 0 -> 循环2: 1 -> 循环3: 0 -> 循环4: 1 -> 循环5: 0
  ```

### 4. random - 随机模式
- **行为**：从指定循环开始随机输出0或1
- **适用场景**：模拟不确定的状态变化
- **示例**：
  ```
  默认值: 0, 触发循环: 2, 模式: random
  循环1: 0 -> 循环2: 1 -> 循环3: 0 -> 循环4: 1 -> 循环5: 1
  ```

## 配置优先级

配置的优先级从高到低：
1. **固定值** - 如果设置了固定值，始终输出该值
2. **启用状态** - 如果禁用，始终输出0
3. **触发逻辑** - 根据触发循环和模式计算值
4. **默认值** - 在触发条件未满足时使用

## 实际应用示例

### 示例1：模拟电源故障
```json
{
  "QDDY_GZ": {
    "default_value": 0,
    "trigger_loop": 5,
    "trigger_mode": "持续",
    "fixed_value": "",
    "enabled": true
  }
}
```
**效果**：前4次循环正常（输出0），第5次循环开始故障（持续输出1）

### 示例2：模拟门开关动作
```json
{
  "YC_KM": {
    "default_value": 0,
    "trigger_loop": 2,
    "trigger_mode": "toggle",
    "fixed_value": "",
    "enabled": true
  }
}
```
**效果**：从第2次循环开始，每次循环在开门(1)和关门(0)之间切换

### 示例3：模拟瞬时按钮
```json
{
  "LCB_JDKM": {
    "default_value": 0,
    "trigger_loop": 3,
    "trigger_mode": "once",
    "fixed_value": "",
    "enabled": true
  }
}
```
**效果**：第3次循环时按钮被按下（输出1），其他时候为0

### 示例4：模拟随机干扰
```json
{
  "ZAWTC_BJ": {
    "default_value": 0,
    "trigger_loop": 1,
    "trigger_mode": "random",
    "fixed_value": "",
    "enabled": true
  }
}
```
**效果**：从第1次循环开始随机产生障碍物检测报警

## 配置界面使用

### 字段配置表格说明
- **字段名称**：数据字段的标识符
- **描述**：字段的中文说明
- **类型**：显示"开关量"
- **默认值**：下拉选择0或1
- **触发循环**：输入数字，0表示不触发
- **触发模式**：下拉选择触发模式
- **固定值**：可选，强制设置输出值
- **启用**：勾选框，控制字段是否生效

### 配置技巧
1. **测试简单场景**：先设置固定值验证基本功能
2. **模拟复杂场景**：组合使用不同的触发模式
3. **调试配置**：使用较少的循环次数快速验证效果
4. **批量配置**：使用导入导出功能在多个车站间复制配置

## 注意事项

### 循环次数设置
- 触发循环从1开始计数
- 如果触发循环大于总循环次数，字段将始终为默认值
- 设置为0表示不触发，始终为默认值

### 状态管理
- 每个字段的状态独立管理
- 切换模式会记住上次的状态
- 重新开始发送时会重置所有状态

### 性能考虑
- 大量字段使用随机模式可能略微影响性能
- 建议合理设置触发循环，避免不必要的计算

## 测试验证

可以使用提供的测试脚本验证配置逻辑：
```bash
python test_digital_logic.py
```

该脚本会测试所有触发模式的行为，确保配置符合预期。

## 配置文件格式

开关量字段的完整配置格式：
```json
{
  "field_name": {
    "default_value": 0,        // 默认值：0或1
    "trigger_loop": 3,         // 触发循环：正整数
    "trigger_mode": "once",    // 触发模式：once/toggle/random/持续
    "fixed_value": "",         // 固定值：可选，"0"或"1"
    "enabled": true            // 启用状态：true或false
  }
}
```

这种配置方式让开关量字段的行为更加可控和符合实际应用场景。
