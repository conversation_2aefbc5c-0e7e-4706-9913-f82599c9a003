"""
数据字段配置对话框
支持对每个数据字段进行详细配置
"""
import tkinter as tk
from tkinter import ttk, messagebox
import json
from typing import Dict, Any
from data_fields_config import DATA_FIELDS_CONFIG


class FieldConfigDialog:
    """数据字段配置对话框"""
    
    def __init__(self, parent, station_config=None):
        self.parent = parent
        self.result = None
        self.station_config = station_config or {}
        self.field_vars = {}
        
        self.create_dialog()
    
    def create_dialog(self):
        """创建配置对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("数据字段详细配置")
        self.dialog.geometry("900x700")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)
        
        # 为每个数据类型创建标签页
        for data_type, config in DATA_FIELDS_CONFIG.items():
            self.create_data_type_tab(data_type, config)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))
        
        ttk.Button(button_frame, text="确定", command=self.save_config).pack(side="right", padx=5)
        ttk.Button(button_frame, text="取消", command=self.close_dialog).pack(side="right", padx=5)
        ttk.Button(button_frame, text="重置为默认", command=self.reset_to_default).pack(side="left", padx=5)
        ttk.Button(button_frame, text="导入配置", command=self.import_config).pack(side="left", padx=5)
        ttk.Button(button_frame, text="导出配置", command=self.export_config).pack(side="left", padx=5)

        # 绑定窗口关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.close_dialog)
    
    def create_data_type_tab(self, data_type: str, config: Dict):
        """为数据类型创建标签页"""
        # 创建标签页框架
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text=f"{data_type} - {config['description']}")
        
        # 创建滚动框架
        canvas = tk.Canvas(tab_frame)
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 数据类型信息
        info_frame = ttk.LabelFrame(scrollable_frame, text="数据类型信息")
        info_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(info_frame, text=f"类型: {data_type}").pack(anchor="w", padx=5, pady=2)
        ttk.Label(info_frame, text=f"描述: {config['description']}").pack(anchor="w", padx=5, pady=2)
        ttk.Label(info_frame, text=f"主题后缀: {config['topic_suffix']}").pack(anchor="w", padx=5, pady=2)
        
        if config.get('has_direction'):
            ttk.Label(info_frame, text="支持方向: 上行(SX) / 下行(XX)").pack(anchor="w", padx=5, pady=2)
        if config.get('has_index'):
            ttk.Label(info_frame, text="支持设备编号: 是").pack(anchor="w", padx=5, pady=2)
        
        # 字段配置
        fields_frame = ttk.LabelFrame(scrollable_frame, text="字段配置")
        fields_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建表格标题
        header_frame = ttk.Frame(fields_frame)
        header_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(header_frame, text="字段名称", width=18).grid(row=0, column=0, padx=2, sticky="w")
        ttk.Label(header_frame, text="描述", width=25).grid(row=0, column=1, padx=2, sticky="w")
        ttk.Label(header_frame, text="类型", width=8).grid(row=0, column=2, padx=2, sticky="w")
        ttk.Label(header_frame, text="默认值", width=8).grid(row=0, column=3, padx=2, sticky="w")
        ttk.Label(header_frame, text="触发循环", width=8).grid(row=0, column=4, padx=2, sticky="w")
        ttk.Label(header_frame, text="触发模式", width=10).grid(row=0, column=5, padx=2, sticky="w")
        ttk.Label(header_frame, text="固定值", width=8).grid(row=0, column=6, padx=2, sticky="w")
        ttk.Label(header_frame, text="启用", width=6).grid(row=0, column=7, padx=2, sticky="w")
        
        # 添加分隔线
        separator = ttk.Separator(fields_frame, orient="horizontal")
        separator.pack(fill="x", padx=5, pady=2)
        
        # 字段配置区域
        fields_config_frame = ttk.Frame(fields_frame)
        fields_config_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 初始化字段变量字典
        if data_type not in self.field_vars:
            self.field_vars[data_type] = {}
        
        # 为每个字段创建配置行
        row = 0
        for field_name, field_config in config['fields'].items():
            self.create_field_config_row(
                fields_config_frame, 
                data_type, 
                field_name, 
                field_config, 
                row
            )
            row += 1
        
        # 打包滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件（只在canvas区域内生效）
        def _on_mousewheel(event):
            try:
                if canvas.winfo_exists():
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                # 组件已被销毁，忽略错误
                pass

        # 只在canvas上绑定滚轮事件，而不是全局绑定
        canvas.bind("<MouseWheel>", _on_mousewheel)

        # 为了让滚轮事件在canvas区域内生效，需要设置焦点
        def _on_canvas_enter(event):
            canvas.focus_set()

        def _on_canvas_leave(event):
            self.dialog.focus_set()

        canvas.bind("<Enter>", _on_canvas_enter)
        canvas.bind("<Leave>", _on_canvas_leave)
    
    def create_field_config_row(self, parent, data_type: str, field_name: str, field_config: Dict, row: int):
        """创建字段配置行"""
        # 获取当前配置值
        current_config = self.get_current_field_config(data_type, field_name, field_config)

        # 字段名称
        ttk.Label(parent, text=field_name, width=18).grid(row=row, column=0, padx=2, pady=1, sticky="w")

        # 描述
        ttk.Label(parent, text=field_config['description'], width=25).grid(row=row, column=1, padx=2, pady=1, sticky="w")

        # 类型
        type_text = "开关量" if field_config['type'] == 'digital' else "数字量"
        ttk.Label(parent, text=type_text, width=8).grid(row=row, column=2, padx=2, pady=1, sticky="w")

        # 创建变量
        field_vars = {}

        if field_config['type'] == 'digital':
            # 开关量配置
            # 默认值
            default_var = tk.IntVar(value=current_config.get('default_value', 0))
            default_combo = ttk.Combobox(parent, textvariable=default_var, values=[0, 1], width=6, state="readonly")
            default_combo.grid(row=row, column=3, padx=2, pady=1)
            field_vars['default_value'] = default_var

            # 触发循环次数
            trigger_var = tk.IntVar(value=current_config.get('trigger_loop', 0))
            trigger_entry = ttk.Entry(parent, textvariable=trigger_var, width=8)
            trigger_entry.grid(row=row, column=4, padx=2, pady=1)
            field_vars['trigger_loop'] = trigger_var

            # 触发模式
            from digital_field_generator import digital_generator
            trigger_modes = list(digital_generator.get_trigger_modes().keys())
            trigger_mode_var = tk.StringVar(value=current_config.get('trigger_mode', 'once'))
            trigger_mode_combo = ttk.Combobox(parent, textvariable=trigger_mode_var, values=trigger_modes, width=8, state="readonly")
            trigger_mode_combo.grid(row=row, column=5, padx=2, pady=1)
            field_vars['trigger_mode'] = trigger_mode_var

        else:
            # 数字量配置
            # 最小值和最大值（使用原来的逻辑）
            min_var = tk.IntVar(value=current_config.get('range', [0, 100])[0])
            max_var = tk.IntVar(value=current_config.get('range', [0, 100])[1])

            # 默认值显示为范围
            range_label = ttk.Label(parent, text=f"{min_var.get()}-{max_var.get()}", width=8)
            range_label.grid(row=row, column=3, padx=2, pady=1)

            # 最小值
            min_entry = ttk.Entry(parent, textvariable=min_var, width=8)
            min_entry.grid(row=row, column=4, padx=2, pady=1)

            # 最大值
            max_entry = ttk.Entry(parent, textvariable=max_var, width=8)
            max_entry.grid(row=row, column=5, padx=2, pady=1)

            field_vars['min'] = min_var
            field_vars['max'] = max_var

            # 更新范围显示
            def update_range_display(*args):
                range_label.config(text=f"{min_var.get()}-{max_var.get()}")
            min_var.trace('w', update_range_display)
            max_var.trace('w', update_range_display)

        # 固定值选项
        fixed_var = tk.StringVar(value=current_config.get('fixed_value', ''))
        fixed_entry = ttk.Entry(parent, textvariable=fixed_var, width=8)
        fixed_entry.grid(row=row, column=6, padx=2, pady=1)
        field_vars['fixed'] = fixed_var

        # 启用选项
        enabled_var = tk.BooleanVar(value=current_config.get('enabled', True))
        enabled_check = ttk.Checkbutton(parent, variable=enabled_var)
        enabled_check.grid(row=row, column=7, padx=2, pady=1)
        field_vars['enabled'] = enabled_var

        # 保存变量
        self.field_vars[data_type][field_name] = field_vars
    
    def get_current_field_config(self, data_type: str, field_name: str, default_config: Dict) -> Dict:
        """获取当前字段配置"""
        # 从station_config中获取配置，如果没有则使用默认值
        field_configs = self.station_config.get('field_configs', {})
        data_type_configs = field_configs.get(data_type, {})
        field_config = data_type_configs.get(field_name, {})

        if default_config['type'] == 'digital':
            return {
                'default_value': field_config.get('default_value', default_config.get('default_value', 0)),
                'trigger_loop': field_config.get('trigger_loop', 0),
                'trigger_mode': field_config.get('trigger_mode', 'once'),
                'fixed_value': field_config.get('fixed_value', ''),
                'enabled': field_config.get('enabled', True)
            }
        else:
            return {
                'range': field_config.get('range', default_config.get('default', [0, 100])),
                'fixed_value': field_config.get('fixed_value', ''),
                'enabled': field_config.get('enabled', True)
            }
    
    def save_config(self):
        """保存配置"""
        try:
            field_configs = {}

            for data_type, fields in self.field_vars.items():
                field_configs[data_type] = {}

                for field_name, vars_dict in fields.items():
                    fixed_val = vars_dict['fixed'].get().strip()
                    enabled = vars_dict['enabled'].get()

                    # 获取字段类型
                    field_type = DATA_FIELDS_CONFIG[data_type]['fields'][field_name]['type']

                    if field_type == 'digital':
                        # 开关量字段
                        default_value = vars_dict['default_value'].get()
                        trigger_loop = vars_dict['trigger_loop'].get()
                        trigger_mode = vars_dict['trigger_mode'].get()

                        # 验证触发循环次数
                        if trigger_loop < 0:
                            messagebox.showerror("错误", f"{data_type}.{field_name}: 触发循环次数不能为负数")
                            return

                        field_configs[data_type][field_name] = {
                            'default_value': default_value,
                            'trigger_loop': trigger_loop,
                            'trigger_mode': trigger_mode,
                            'fixed_value': fixed_val,
                            'enabled': enabled
                        }
                    else:
                        # 数字量字段
                        min_val = vars_dict['min'].get()
                        max_val = vars_dict['max'].get()

                        # 验证范围
                        if min_val > max_val:
                            messagebox.showerror("错误", f"{data_type}.{field_name}: 最小值不能大于最大值")
                            return

                        field_configs[data_type][field_name] = {
                            'range': [min_val, max_val],
                            'fixed_value': fixed_val,
                            'enabled': enabled
                        }

            self.result = field_configs
            self.close_dialog()

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def close_dialog(self):
        """安全关闭对话框"""
        try:
            # 取消所有绑定的事件
            self.dialog.unbind_all("<MouseWheel>")
            # 销毁对话框
            self.dialog.destroy()
        except Exception:
            # 忽略销毁过程中的任何错误
            pass
    
    def reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认", "确定要重置所有字段为默认配置吗？"):
            for data_type, fields in self.field_vars.items():
                for field_name, vars_dict in fields.items():
                    field_config = DATA_FIELDS_CONFIG[data_type]['fields'][field_name]

                    if field_config['type'] == 'digital':
                        # 开关量字段
                        vars_dict['default_value'].set(field_config.get('default_value', 0))
                        vars_dict['trigger_loop'].set(0)
                        vars_dict['trigger_mode'].set('once')
                    else:
                        # 数字量字段
                        default_range = field_config.get('default', [0, 100])
                        vars_dict['min'].set(default_range[0])
                        vars_dict['max'].set(default_range[1])

                    vars_dict['fixed'].set('')
                    vars_dict['enabled'].set(True)
    
    def import_config(self):
        """导入配置"""
        from tkinter import filedialog
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="导入字段配置"
            )
            
            if filename:
                with open(filename, "r", encoding="utf-8") as f:
                    imported_config = json.load(f)
                
                # 应用导入的配置
                for data_type, fields in imported_config.items():
                    if data_type in self.field_vars:
                        for field_name, config in fields.items():
                            if field_name in self.field_vars[data_type]:
                                vars_dict = self.field_vars[data_type][field_name]
                                vars_dict['min'].set(config['range'][0])
                                vars_dict['max'].set(config['range'][1])
                                vars_dict['fixed'].set(config.get('fixed_value', ''))
                                vars_dict['enabled'].set(config.get('enabled', True))
                
                messagebox.showinfo("成功", "配置导入成功")
                
        except Exception as e:
            messagebox.showerror("错误", f"导入配置失败: {e}")
    
    def export_config(self):
        """导出配置"""
        from tkinter import filedialog
        try:
            # 收集当前配置
            current_config = {}
            for data_type, fields in self.field_vars.items():
                current_config[data_type] = {}
                for field_name, vars_dict in fields.items():
                    current_config[data_type][field_name] = {
                        'range': [vars_dict['min'].get(), vars_dict['max'].get()],
                        'fixed_value': vars_dict['fixed'].get().strip(),
                        'enabled': vars_dict['enabled'].get()
                    }
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="导出字段配置"
            )
            
            if filename:
                with open(filename, "w", encoding="utf-8") as f:
                    json.dump(current_config, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("成功", "配置导出成功")
                
        except Exception as e:
            messagebox.showerror("错误", f"导出配置失败: {e}")
