#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Python 2.7 compatible startup script
"""
import sys
import os

def main():
    print("=== Multi-Station MQTT Data Generator (Python 2.7) ===")
    print("Python version: " + str(sys.version))
    print("Working directory: " + str(os.getcwd()))
    
    try:
        print("1. Checking dependencies...")
        
        # Check Tkinter (Python 2 style)
        try:
            import Tkin<PERSON> as tk
            import ttk
            import tkMessageBox as messagebox
            import tkFileDialog as filedialog
            print("   OK: Tkinter available")
        except ImportError as e:
            print("   ERROR: Tkinter not available: " + str(e))
            return False
        
        # Check paho-mqtt (optional for this test)
        try:
            import paho.mqtt.client as mqtt
            print("   OK: paho-mqtt available")
        except ImportError as e:
            print("   WARNING: paho-mqtt not available: " + str(e))
            print("   (This is OK for GUI testing, but needed for MQTT functionality)")
        
        print("2. Note: The original GUI is designed for Python 3")
        print("   It uses features not available in Python 2.7")
        print("   You may need to:")
        print("   - Update to a newer macOS version")
        print("   - Install Python 3.9+ with proper macOS support")
        print("   - Or modify the code for Python 2.7 compatibility")
        
        print("3. Creating a simple test window...")
        
        # Create a simple test window
        root = tk.Tk()
        root.title("MQTT Data Generator - Test Window")
        root.geometry("400x300")
        
        # Add some basic widgets
        label = tk.Label(root, text="MQTT Data Generator Test", font=("Arial", 16))
        label.pack(pady=20)
        
        info_text = tk.Text(root, height=10, width=50)
        info_text.pack(pady=10)
        
        info_text.insert(tk.END, "System Information:\n")
        info_text.insert(tk.END, "Python: " + str(sys.version) + "\n")
        info_text.insert(tk.END, "Platform: " + str(sys.platform) + "\n")
        info_text.insert(tk.END, "Working Dir: " + str(os.getcwd()) + "\n\n")
        info_text.insert(tk.END, "Status: Basic GUI test successful!\n")
        info_text.insert(tk.END, "The full application requires Python 3.\n")
        
        def close_window():
            print("Closing test window...")
            root.destroy()
        
        close_btn = tk.Button(root, text="Close", command=close_window)
        close_btn.pack(pady=10)
        
        print("4. Test window created successfully!")
        print("   Window should be visible now.")
        print("   Close the window to continue...")
        
        # Start the GUI loop
        root.mainloop()
        
        print("5. Test window closed")
        return True
        
    except Exception as e:
        print("Error: " + str(e))
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\nBasic GUI test completed successfully!")
        print("For the full application, please use Python 3 on a compatible system.")
    else:
        print("\nTest failed. Please check the error messages above.")
