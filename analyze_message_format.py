"""
分析message.json文件，生成正确的字段配置
"""
import json
import re

def analyze_message_json():
    """分析message.json文件"""
    print("=== 分析message.json文件 ===\n")
    
    # 读取message.json文件
    with open("message.json", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 分割每个JSON对象 - message.json包含多个连续的JSON对象
    json_objects = []

    # 简单的方法：按照 }{ 分割，然后重新组装
    parts = content.split('}\n{')

    for i, part in enumerate(parts):
        if i == 0:
            # 第一个部分
            json_str = part + '}'
        elif i == len(parts) - 1:
            # 最后一个部分
            json_str = '{' + part
        else:
            # 中间部分
            json_str = '{' + part + '}'

        try:
            obj = json.loads(json_str)
            json_objects.append(obj)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"问题字符串: {json_str[:100]}...")
            continue
    
    print(f"找到 {len(json_objects)} 个消息对象")
    
    # 按设备类型分组分析
    device_fields = {}
    
    for obj in json_objects:
        topic = obj.get("topic", "")
        payload = obj.get("payload", {})
        
        # 解析topic获取设备类型
        # 格式: L03/S18/PSD/POWER 或 L03/S18/PSD/SX/ASD01
        topic_parts = topic.split("/")
        if len(topic_parts) >= 4:
            if len(topic_parts) == 4:
                # 单一设备类型 (POWER, PS)
                device_type = topic_parts[3]
            else:
                # 多设备类型 (ASD01, EED01, etc.)
                device_name = topic_parts[4]
                if device_name.startswith("ASD"):
                    device_type = "ASD"
                elif device_name.startswith("EED"):
                    device_type = "EED"
                elif device_name.startswith("MSD"):
                    device_type = "MSD"
                elif device_name.startswith("PDS"):
                    device_type = "PDS"
                elif device_name.startswith("PSL"):
                    device_type = "PSL"
                elif device_name == "IBP":
                    device_type = "IBP"
                elif device_name == "PEDC":
                    device_type = "PEDC"
                elif device_name == "SIG":
                    device_type = "SIG"
                else:
                    continue
            
            # 收集字段信息
            if device_type not in device_fields:
                device_fields[device_type] = {}
            
            for field_name, field_value in payload.items():
                if field_name == "timestamp":
                    continue
                
                if field_name not in device_fields[device_type]:
                    device_fields[device_type][field_name] = {
                        "values": set(),
                        "type": None
                    }
                
                device_fields[device_type][field_name]["values"].add(field_value)
    
    # 分析字段类型
    for device_type, fields in device_fields.items():
        for field_name, field_info in fields.items():
            values = field_info["values"]
            
            # 判断字段类型
            if all(isinstance(v, int) and v in [0, 1] for v in values):
                field_info["type"] = "digital"
            elif all(isinstance(v, (int, float)) for v in values):
                field_info["type"] = "analog"
                field_info["range"] = [min(values), max(values)]
            else:
                field_info["type"] = "unknown"
    
    return device_fields

def generate_correct_field_configs(device_fields):
    """生成正确的字段配置"""
    print("=== 生成正确的字段配置 ===\n")
    
    field_configs = {}
    
    for device_type, fields in device_fields.items():
        field_configs[device_type] = {}
        
        print(f"{device_type} 设备:")
        
        for field_name, field_info in fields.items():
            if field_info["type"] == "digital":
                # 开关量字段
                config = {
                    "default_value": 0,
                    "trigger_loop": 0,
                    "trigger_mode": "once",
                    "fixed_value": "",
                    "enabled": True
                }
                field_configs[device_type][field_name] = config
                print(f"  {field_name} (开关量): 值域 {sorted(field_info['values'])}")
                
            elif field_info["type"] == "analog":
                # 数字量字段
                config = {
                    "range": list(field_info["range"]),
                    "fixed_value": "",
                    "enabled": True
                }
                field_configs[device_type][field_name] = config
                print(f"  {field_name} (数字量): 范围 {field_info['range']}")
            
            else:
                print(f"  {field_name} (未知类型): 值 {field_info['values']}")
        
        print()
    
    return field_configs

def compare_with_current_config(correct_configs):
    """与当前配置对比"""
    print("=== 与当前配置对比 ===\n")
    
    try:
        from data_fields_config import DATA_FIELDS_CONFIG
        
        for device_type in correct_configs.keys():
            print(f"{device_type} 设备对比:")
            
            if device_type in DATA_FIELDS_CONFIG:
                current_fields = set(DATA_FIELDS_CONFIG[device_type]["fields"].keys())
                correct_fields = set(correct_configs[device_type].keys())
                
                missing_fields = correct_fields - current_fields
                extra_fields = current_fields - correct_fields
                
                if missing_fields:
                    print(f"  缺失字段: {missing_fields}")
                if extra_fields:
                    print(f"  多余字段: {extra_fields}")
                if not missing_fields and not extra_fields:
                    print(f"  ✅ 字段完全匹配")
            else:
                print(f"  ❌ 当前配置中缺少 {device_type} 设备")
            
            print()
    
    except ImportError:
        print("无法导入当前配置文件")

def save_correct_config(field_configs):
    """保存正确的配置"""
    print("=== 保存正确配置 ===\n")
    
    # 创建完整的配置结构
    correct_data_fields_config = {}
    
    device_descriptions = {
        "POWER": "电源系统",
        "PS": "电源监控",
        "ASD": "自动滑动门",
        "EED": "紧急疏散门",
        "MSD": "手动滑动门",
        "IBP": "综合后备盘",
        "PDS": "乘客检测系统",
        "PEDC": "乘客紧急对讲",
        "PSL": "站台安全门",
        "SIG": "信号系统"
    }
    
    for device_type, fields in field_configs.items():
        correct_data_fields_config[device_type] = {
            "description": device_descriptions.get(device_type, device_type),
            "fields": {}
        }
        
        for field_name, field_config in fields.items():
            if "default_value" in field_config:
                # 开关量字段
                correct_data_fields_config[device_type]["fields"][field_name] = {
                    "type": "digital",
                    "description": field_name,
                    "default_value": field_config["default_value"]
                }
            else:
                # 数字量字段
                correct_data_fields_config[device_type]["fields"][field_name] = {
                    "type": "analog",
                    "description": field_name,
                    "default": field_config["range"]
                }
    
    # 保存到文件
    with open("correct_data_fields_config.py", "w", encoding="utf-8") as f:
        f.write('"""\n')
        f.write('基于message.json生成的正确数据字段配置\n')
        f.write('"""\n\n')
        f.write('DATA_FIELDS_CONFIG = ')
        f.write(json.dumps(correct_data_fields_config, ensure_ascii=False, indent=2))
    
    print("✅ 正确配置已保存到: correct_data_fields_config.py")
    
    # 生成字段配置示例
    example_station = {
        "station_id": "S99",
        "station_name": "示例车站",
        "enabled": True,
        "field_configs": field_configs
    }
    
    with open("correct_station_config.json", "w", encoding="utf-8") as f:
        json.dump(example_station, f, ensure_ascii=False, indent=2)
    
    print("✅ 车站配置示例已保存到: correct_station_config.json")

def print_summary(device_fields):
    """打印总结信息"""
    print("=== 总结信息 ===\n")
    
    total_devices = len(device_fields)
    total_fields = sum(len(fields) for fields in device_fields.values())
    
    print(f"设备类型总数: {total_devices}")
    print(f"字段总数: {total_fields}")
    print()
    
    for device_type, fields in device_fields.items():
        digital_count = sum(1 for f in fields.values() if f["type"] == "digital")
        analog_count = sum(1 for f in fields.values() if f["type"] == "analog")
        
        print(f"{device_type}: {len(fields)} 个字段 (开关量: {digital_count}, 数字量: {analog_count})")

if __name__ == "__main__":
    # 分析message.json
    device_fields = analyze_message_json()
    
    # 生成正确配置
    correct_configs = generate_correct_field_configs(device_fields)
    
    # 与当前配置对比
    compare_with_current_config(correct_configs)
    
    # 保存正确配置
    save_correct_config(correct_configs)
    
    # 打印总结
    print_summary(device_fields)
    
    print("\n=== 使用建议 ===")
    print("1. 使用生成的 correct_data_fields_config.py 替换当前的 data_fields_config.py")
    print("2. 使用 correct_station_config.json 作为车站配置的参考")
    print("3. 重新启动应用程序以应用新的字段配置")
    print("4. 验证生成的数据是否与message.json格式一致")
