"""
测试字段配置的正确性
"""
import json
from data_fields_config import DATA_FIELDS_CONFIG

def test_field_config_structure():
    """测试字段配置结构"""
    print("=== 字段配置结构测试 ===\n")
    
    # 生成默认字段配置
    field_configs = {}
    
    for device_type, device_config in DATA_FIELDS_CONFIG.items():
        field_configs[device_type] = {}
        
        print(f"{device_type} - {device_config['description']}:")
        
        for field_name, field_info in device_config["fields"].items():
            if field_info["type"] == "digital":
                # 开关量字段配置
                config = {
                    "default_value": field_info.get("default_value", 0),
                    "trigger_loop": 0,
                    "trigger_mode": "once",
                    "fixed_value": "",
                    "enabled": True
                }
                field_configs[device_type][field_name] = config
                print(f"  {field_name} (开关量): {config}")
            else:
                # 数字量字段配置
                default_range = field_info.get("default", [0, 100])
                config = {
                    "range": default_range,
                    "fixed_value": "",
                    "enabled": True
                }
                field_configs[device_type][field_name] = config
                print(f"  {field_name} (数字量): {config}")
        
        print()
    
    return field_configs

def test_specific_device_configs():
    """测试特定设备的配置"""
    print("=== 特定设备配置测试 ===\n")
    
    # 测试POWER设备
    print("1. POWER设备字段:")
    power_fields = DATA_FIELDS_CONFIG["POWER"]["fields"]
    digital_count = 0
    for field_name, field_info in power_fields.items():
        if field_info["type"] == "digital":
            digital_count += 1
            print(f"   {field_name}: {field_info['description']}")
    print(f"   总计: {digital_count} 个开关量字段\n")
    
    # 测试PS设备
    print("2. PS设备字段:")
    ps_fields = DATA_FIELDS_CONFIG["PS"]["fields"]
    analog_count = 0
    for field_name, field_info in ps_fields.items():
        if field_info["type"] == "analog":
            analog_count += 1
            print(f"   {field_name}: {field_info['description']} (范围: {field_info.get('default', [0, 100])})")
    print(f"   总计: {analog_count} 个数字量字段\n")
    
    # 测试ASD设备
    print("3. ASD设备字段:")
    asd_fields = DATA_FIELDS_CONFIG["ASD"]["fields"]
    digital_count = analog_count = 0
    for field_name, field_info in asd_fields.items():
        if field_info["type"] == "digital":
            digital_count += 1
        else:
            analog_count += 1
    print(f"   开关量字段: {digital_count} 个")
    print(f"   数字量字段: {analog_count} 个")
    print(f"   总计: {digital_count + analog_count} 个字段\n")

def compare_with_existing_config():
    """与现有配置对比"""
    print("=== 与现有配置对比 ===\n")
    
    try:
        # 读取现有配置
        with open("stations.json", "r", encoding="utf-8") as f:
            existing_config = json.load(f)
        
        if existing_config["stations"]:
            station = existing_config["stations"][0]
            existing_fields = station.get("field_configs", {})
            
            print("现有配置中的设备类型:")
            for device_type in existing_fields.keys():
                field_count = len(existing_fields[device_type])
                print(f"  {device_type}: {field_count} 个字段")
            
            print("\n标准配置中的设备类型:")
            for device_type, device_config in DATA_FIELDS_CONFIG.items():
                field_count = len(device_config["fields"])
                print(f"  {device_type}: {field_count} 个字段")
            
            # 检查缺失的设备类型
            missing_devices = set(DATA_FIELDS_CONFIG.keys()) - set(existing_fields.keys())
            if missing_devices:
                print(f"\n缺失的设备类型: {missing_devices}")
            
            # 检查多余的设备类型
            extra_devices = set(existing_fields.keys()) - set(DATA_FIELDS_CONFIG.keys())
            if extra_devices:
                print(f"多余的设备类型: {extra_devices}")
            
            # 检查ASD字段配置格式
            if "ASD" in existing_fields:
                print("\nASD字段配置格式检查:")
                asd_config = existing_fields["ASD"]
                
                for field_name, field_config in list(asd_config.items())[:5]:  # 只检查前5个
                    print(f"  {field_name}: {field_config}")
                    
                    # 检查配置格式
                    if "default_value" in field_config:
                        print(f"    ✅ 开关量配置格式正确")
                    elif "range" in field_config:
                        print(f"    ✅ 数字量配置格式正确")
                    else:
                        print(f"    ❌ 配置格式不正确")
        
    except FileNotFoundError:
        print("未找到stations.json文件")
    except Exception as e:
        print(f"读取配置文件失败: {e}")

def generate_correct_config_example():
    """生成正确的配置示例"""
    print("\n=== 生成正确配置示例 ===\n")
    
    # 生成完整的字段配置
    field_configs = {}
    
    for device_type, device_config in DATA_FIELDS_CONFIG.items():
        field_configs[device_type] = {}
        
        for field_name, field_info in device_config["fields"].items():
            if field_info["type"] == "digital":
                # 开关量字段配置
                field_configs[device_type][field_name] = {
                    "default_value": field_info.get("default_value", 0),
                    "trigger_loop": 0,
                    "trigger_mode": "once",
                    "fixed_value": "",
                    "enabled": True
                }
            else:
                # 数字量字段配置
                default_range = field_info.get("default", [0, 100])
                field_configs[device_type][field_name] = {
                    "range": default_range,
                    "fixed_value": "",
                    "enabled": True
                }
    
    # 创建示例车站配置
    example_station = {
        "station_id": "S99",
        "station_name": "示例车站",
        "enabled": True,
        "config": {
            "asd_count": {"sx": 30, "xx": 30},
            "eed_count": {"sx": 8, "xx": 8},
            "msd_count": {"sx": 2, "xx": 2},
            "pds_count": {"sx": 8, "xx": 8},
            "psl_count": {"sx": 3, "xx": 3}
        },
        "field_configs": field_configs
    }
    
    # 保存示例配置
    with open("correct_field_config_example.json", "w", encoding="utf-8") as f:
        json.dump(example_station, f, ensure_ascii=False, indent=2)
    
    print("✅ 正确的字段配置示例已保存到: correct_field_config_example.json")
    
    # 显示统计信息
    total_fields = 0
    digital_fields = 0
    analog_fields = 0
    
    for device_type, fields in field_configs.items():
        device_field_count = len(fields)
        total_fields += device_field_count
        
        for field_name, field_config in fields.items():
            if "default_value" in field_config:
                digital_fields += 1
            else:
                analog_fields += 1
        
        print(f"{device_type}: {device_field_count} 个字段")
    
    print(f"\n总计: {total_fields} 个字段")
    print(f"开关量字段: {digital_fields} 个")
    print(f"数字量字段: {analog_fields} 个")

if __name__ == "__main__":
    test_field_config_structure()
    test_specific_device_configs()
    compare_with_existing_config()
    generate_correct_config_example()
    
    print("\n=== 修复建议 ===")
    print("1. 在multi_station_gui.py中使用正确的field_configs格式")
    print("2. 确保所有设备类型都有完整的字段配置")
    print("3. 开关量字段使用default_value、trigger_loop等配置")
    print("4. 数字量字段使用range配置")
    print("5. 可以参考生成的correct_field_config_example.json文件")
