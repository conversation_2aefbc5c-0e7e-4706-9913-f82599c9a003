#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI启动脚本 - 带错误处理和日志输出
"""
import sys
import os
import traceback

def main():
    print("=== 多车站MQTT数据发生器启动 ===")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    try:
        print("1. 检查依赖模块...")
        
        # 检查tkinter
        try:
            import tkinter as tk
            print("   ✓ tkinter 可用")
        except ImportError as e:
            print(f"   ✗ tkinter 不可用: {e}")
            return False
        
        # 检查paho-mqtt
        try:
            import paho.mqtt.client as mqtt
            print("   ✓ paho-mqtt 可用")
        except ImportError as e:
            print(f"   ✗ paho-mqtt 不可用: {e}")
            print("   请运行: pip install paho-mqtt")
            return False
        
        # 检查本地模块
        try:
            from data_generator import StationDataGenerator
            print("   ✓ data_generator 可用")
        except ImportError as e:
            print(f"   ✗ data_generator 不可用: {e}")
            return False
        
        try:
            from data_fields_config import DATA_FIELDS_CONFIG
            print("   ✓ data_fields_config 可用")
        except ImportError as e:
            print(f"   ✗ data_fields_config 不可用: {e}")
            return False
        
        print("2. 创建GUI应用...")
        
        # 导入主应用
        from multi_station_gui import MultiStationMQTTGenerator
        
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口属性
        root.title("多车站MQTT数据发生器")
        root.geometry("1000x700")
        
        print("3. 初始化应用...")
        app = MultiStationMQTTGenerator(root)
        
        print("4. 启动GUI主循环...")
        print("GUI窗口应该已经打开，如果没有看到窗口，请检查任务栏或Dock")
        
        # 启动主循环
        root.mainloop()
        
        print("5. GUI已关闭")
        return True
        
    except Exception as e:
        print(f"启动失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n程序启动失败，请检查错误信息")
        sys.exit(1)
    else:
        print("\n程序正常退出")
