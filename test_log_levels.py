"""
测试不同日志详细级别的效果
"""
import time

def format_log_content(payload, device_name, detail_level, current_loop, station_id):
    """格式化日志内容"""
    # 过滤掉timestamp字段
    data_fields = {k: v for k, v in payload.items() if k != "timestamp"}
    
    # 根据详细级别格式化数据内容
    if detail_level == "简单":
        # 只显示设备名和字段总数
        fields_str = f"共{len(data_fields)}个字段"
    elif detail_level == "智能":
        # 智能显示重要字段
        important_fields = []
        other_fields = []
        
        for k, v in data_fields.items():
            # 识别重要字段（通常是状态、故障、开关量等）
            if any(keyword in k.upper() for keyword in ['HDM_', 'LCB_', 'YC_', 'GZ', 'BJ', 'STATUS', 'ALARM']):
                if v != 0:  # 非零值更重要
                    important_fields.append(f"{k}={v}")
                else:
                    other_fields.append(f"{k}={v}")
            else:
                other_fields.append(f"{k}={v}")
        
        # 构建显示字符串
        display_parts = []
        
        # 优先显示重要的非零字段
        if important_fields:
            display_parts.extend(important_fields[:6])
        
        # 如果还有空间，显示其他字段
        remaining_space = 8 - len(display_parts)
        if remaining_space > 0 and other_fields:
            display_parts.extend(other_fields[:remaining_space])
        
        fields_str = ", ".join(display_parts)
        
        # 如果还有更多字段，显示总数
        total_shown = len(display_parts)
        if total_shown < len(data_fields):
            fields_str += f", ... (显示{total_shown}/{len(data_fields)}个字段)"
    elif detail_level == "详细":
        # 显示更多字段
        if len(data_fields) <= 15:
            fields_str = ", ".join([f"{k}={v}" for k, v in data_fields.items()])
        else:
            first_fields = list(data_fields.items())[:12]
            fields_str = ", ".join([f"{k}={v}" for k, v in first_fields])
            fields_str += f", ... (显示12/{len(data_fields)}个字段)"
    else:  # 完整
        # 显示所有字段
        fields_str = ", ".join([f"{k}={v}" for k, v in data_fields.items()])
    
    # 记录日志
    timestamp = time.strftime("%H:%M:%S")
    log_message = f"[{timestamp}] 循环{current_loop} - {station_id}/{device_name}: {fields_str}"
    return log_message

def test_log_levels():
    """测试不同日志级别"""
    print("=== 测试不同日志详细级别 ===\n")
    
    # 测试数据 - ASD设备（字段较多）
    asd_payload = {
        "timestamp": 1640000000000,
        "LCB_GL": 0,
        "LCB_ZD": 1,  # 重要字段，非零
        "LCB_JDKM": 0,
        "LCB_JDGM": 0,
        "HDM_ZKDW": 1,  # 重要字段，非零
        "HDM_YKDW": 0,
        "HDM_ZGDW": 0,
        "HDM_YGDW": 1,  # 重要字段，非零
        "YC_KM": 0,
        "YC_GM": 0,
        "DWGL_ZT": 0,
        "SDJS_BJ": 0,
        "ZAWTC_BJ": 1,  # 重要字段，非零
        "DCSJSYC_BJ": 0,
        "DCU_GZ": 0,
        "ZXWKG_GZ": 0,
        "YXWKG_GZ": 0,
        "DJ_GZ": 0,
        "GM_GZ": 0,
        "KM_GZ": 0,
        "DJ_DL": 58,
        "DJ_SD": 58,
        "DJ_SCNJ": 58,
        "QDB_DY": 58,
        "YC_KM_ML_DY": 58,
        "YC_GM_ML_DY": 58
    }
    
    # 测试数据 - EED设备（字段较少）
    eed_payload = {
        "timestamp": 1640000000000,
        "YJM_DK": 1,
        "YJM_GB": 0,
        "YJM_PL": 0
    }
    
    # 测试数据 - POWER设备（字段很多）
    power_payload = {
        "timestamp": 1640000000000,
        "SQZDY_TR": 0,
        "SQBYDY_TR": 1,
        "SD_GZ": 0,
        "QDDY_GZ": 1,  # 重要字段，非零
        "KZDY_GZ": 1,  # 重要字段，非零
        "QDDY_DY_BJ": 1,  # 重要字段，非零
        "KZDY_DY_BJ": 0,
        "QDDC_TR_BJ": 0,
        "KZDC_TR_BJ": 0,
        "QDDC_DY_BJ": 0,
        "KZDC_DY_BJ": 0,
        "XDC_WD_GG_BJ": 0,
        "SX_QDDY_HL1_BJ": 0,
        "SX_QDDY_HL2_BJ": 0,
        "SX_QDDY_HL3_BJ": 0,
        "SX_QDDY_HL4_BJ": 0,
        "SX_QDDY_HL5_BJ": 0,
        "SX_KZDY_SIG_HL_BJ": 1,  # 重要字段，非零
        "SX_KZDY_PSL_HL_BJ": 0,
        "SX_KZDY_IBP_HL_BJ": 1,  # 重要字段，非零
        "SX_KZDY_PEDC_HL_BJ": 1,  # 重要字段，非零
        "SX_KZDY_DCU_HL_BJ": 1,  # 重要字段，非零
        "SX_KZDY_AQ_HL_BJ": 0,
        "SX_KZDY_JXTC_HL_BJ": 0,
        "SX_KZDY_DKD_HL_BJ": 0,
        "SX_KZDY_LWD_HL_BJ": 0,
        "XX_QDDY_HL1_BJ": 0,
        "XX_QDDY_HL2_BJ": 0,
        "XX_QDDY_HL3_BJ": 0,
        "XX_QDDY_HL4_BJ": 0,
        "XX_QDDY_HL5_BJ": 0,
        "XX_KZDY_SIG_HL_BJ": 0,
        "XX_KZDY_PSL_HL_BJ": 0,
        "XX_KZDY_IBP_HL_BJ": 1,  # 重要字段，非零
        "XX_KZDY_PEDC_HL_BJ": 0,
        "XX_KZDY_DCU_HL_BJ": 1,  # 重要字段，非零
        "XX_KZDY_AQ_HL_BJ": 1,  # 重要字段，非零
        "XX_KZDY_JXTC_HL_BJ": 1,  # 重要字段，非零
        "XX_KZDY_DKD_HL_BJ": 0,
        "XX_KZDY_LWD_HL_BJ": 0
    }
    
    test_cases = [
        ("ASD01", asd_payload),
        ("EED01", eed_payload),
        ("POWER", power_payload)
    ]
    
    detail_levels = ["简单", "智能", "详细", "完整"]
    
    for device_name, payload in test_cases:
        print(f"=== {device_name} 设备日志示例 ===")
        
        for level in detail_levels:
            log_msg = format_log_content(payload, device_name, level, 1, "S18")
            print(f"{level:4s}: {log_msg}")
        
        print()

def test_toggle_logging():
    """测试toggle字段的日志记录"""
    print("=== Toggle字段变化日志示例 ===\n")
    
    # 模拟HDM_YGDW字段的toggle变化（每2个循环切换）
    toggle_data = [
        {"HDM_YGDW": 0, "HDM_ZKDW": 1, "LCB_ZD": 0, "ZAWTC_BJ": 0},
        {"HDM_YGDW": 0, "HDM_ZKDW": 1, "LCB_ZD": 0, "ZAWTC_BJ": 0},
        {"HDM_YGDW": 1, "HDM_ZKDW": 0, "LCB_ZD": 1, "ZAWTC_BJ": 1},
        {"HDM_YGDW": 1, "HDM_ZKDW": 0, "LCB_ZD": 1, "ZAWTC_BJ": 1},
        {"HDM_YGDW": 0, "HDM_ZKDW": 1, "LCB_ZD": 0, "ZAWTC_BJ": 0}
    ]
    
    print("智能模式下的Toggle字段变化:")
    for i, data in enumerate(toggle_data, 1):
        payload = {"timestamp": 1640000000000 + i * 1000}
        payload.update(data)
        
        log_msg = format_log_content(payload, "ASD01", "智能", i, "S18")
        print(log_msg)
        time.sleep(0.1)
    
    print()

def demonstrate_log_benefits():
    """演示不同日志级别的优势"""
    print("=== 不同日志级别的优势 ===\n")
    
    print("📊 简单模式:")
    print("   - 优势: 日志简洁，不占用太多空间")
    print("   - 适用: 只关心发送状态，不关心具体数据")
    print("   - 示例: [10:17:33] 循环1 - S18/ASD01: 共26个字段")
    print()
    
    print("🧠 智能模式:")
    print("   - 优势: 自动突出显示重要字段和异常值")
    print("   - 适用: 需要监控关键状态变化")
    print("   - 示例: [10:17:33] 循环1 - S18/ASD01: LCB_ZD=1, HDM_ZKDW=1, ZAWTC_BJ=1, ... (显示6/26个字段)")
    print()
    
    print("📋 详细模式:")
    print("   - 优势: 显示更多字段，便于详细分析")
    print("   - 适用: 调试和详细监控")
    print("   - 示例: 显示前12个字段")
    print()
    
    print("📄 完整模式:")
    print("   - 优势: 显示所有字段，完整信息")
    print("   - 适用: 完整数据审计和调试")
    print("   - 缺点: 日志量大，可能影响性能")
    print()

def performance_comparison():
    """性能对比"""
    print("=== 性能和可读性对比 ===\n")
    
    # 模拟大量字段的设备
    large_payload = {f"FIELD_{i:03d}": i % 2 for i in range(100)}
    large_payload["timestamp"] = 1640000000000
    large_payload["IMPORTANT_GZ"] = 1  # 重要字段
    large_payload["CRITICAL_BJ"] = 1   # 重要字段
    
    print("100个字段的设备在不同模式下的日志长度:")
    
    for level in ["简单", "智能", "详细", "完整"]:
        log_msg = format_log_content(large_payload, "LARGE_DEVICE", level, 1, "S18")
        print(f"{level:4s}: {len(log_msg):4d} 字符 - {log_msg[:100]}...")
    
    print()
    print("建议:")
    print("- 正常监控: 使用'智能'模式")
    print("- 性能优先: 使用'简单'模式")
    print("- 调试问题: 使用'详细'或'完整'模式")
    print("- 大量车站: 建议使用'简单'或'智能'模式")

if __name__ == "__main__":
    test_log_levels()
    test_toggle_logging()
    demonstrate_log_benefits()
    performance_comparison()
    
    print("\n=== 使用指南 ===")
    print("1. 在应用界面中选择合适的日志详细级别")
    print("2. '智能'模式会自动突出显示重要的非零字段")
    print("3. 可以根据需要随时切换日志级别")
    print("4. 建议在正常使用时选择'智能'模式")
    print("5. 调试时可以临时切换到'详细'或'完整'模式")
