#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI startup script with error handling and logging
"""
import sys
import os
import traceback

def main():
    print("=== Multi-Station MQTT Data Generator Startup ===")
    print("Python version: " + sys.version)
    print("Working directory: " + os.getcwd())
    
    try:
        print("1. Checking dependencies...")
        
        # Check tkinter
        try:
            import tkinter as tk
            print("   OK: tkinter available")
        except ImportError as e:
            print("   ERROR: tkinter not available: " + str(e))
            return False

        # Check paho-mqtt
        try:
            import paho.mqtt.client as mqtt
            print("   OK: paho-mqtt available")
        except ImportError as e:
            print("   ERROR: paho-mqtt not available: " + str(e))
            print("   Please run: pip install paho-mqtt")
            return False

        # Check local modules
        try:
            from data_generator import StationDataGenerator
            print("   OK: data_generator available")
        except ImportError as e:
            print("   ERROR: data_generator not available: " + str(e))
            return False

        try:
            from data_fields_config import DATA_FIELDS_CONFIG
            print("   OK: data_fields_config available")
        except ImportError as e:
            print("   ERROR: data_fields_config not available: " + str(e))
            return False
        
        print("2. Creating GUI application...")
        
        # Import main application
        from multi_station_gui import MultiStationMQTTGenerator
        
        # Create main window
        root = tk.Tk()
        
        # Set window properties
        root.title("Multi-Station MQTT Data Generator")
        root.geometry("1000x700")
        
        print("3. Initializing application...")
        app = MultiStationMQTTGenerator(root)
        
        print("4. Starting GUI main loop...")
        print("GUI window should be open now. Check taskbar or Dock if not visible.")
        
        # Start main loop
        root.mainloop()
        
        print("5. GUI closed")
        return True
        
    except Exception as e:
        print("Startup failed: " + str(e))
        print("Detailed error information:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\nProgram startup failed, please check error messages")
        sys.exit(1)
    else:
        print("\nProgram exited normally")
