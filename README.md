# 站台门模拟数据发生器

本项目是一个站台门模拟数据发生器，具备图形化界面，可配置MQTT服务器地址，用于模拟站台门数据并发送到MQTT服务器。

## 环境要求
- Python 3.x

## 安装依赖
```bash
pip install -r requirements.txt
```

## 运行项目
```bash
python main.py
```

## 使用说明
1. 打开程序后，在"MQTT服务器地址"输入框中输入MQTT服务器地址，默认值为`localhost`。
2. 在"MQTT服务器端口"输入框中输入MQTT服务器端口，默认值为`1883`。
3. 点击"开始发送"按钮，程序将开始生成模拟数据并发送到指定的MQTT服务器。
4. 点击"停止发送"按钮，程序将停止发送数据。

## 配置说明
- `message.json`：参考数据格式文件。
- `main.py`：主程序文件。
- `requirements.txt`：项目依赖文件。