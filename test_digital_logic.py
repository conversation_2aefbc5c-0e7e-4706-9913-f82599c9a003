"""
测试开关量逻辑的脚本
"""
from digital_field_generator import digital_generator

def test_digital_logic():
    """测试开关量逻辑"""
    print("=== 开关量逻辑测试 ===\n")
    
    # 重置状态
    digital_generator.reset_field_states()
    
    # 测试配置
    test_configs = [
        {
            'name': '单次触发测试',
            'config': {
                'default_value': 0,
                'trigger_loop': 3,
                'trigger_mode': 'once',
                'enabled': True,
                'fixed_value': ''
            },
            'total_loops': 5
        },
        {
            'name': '持续模式测试',
            'config': {
                'default_value': 0,
                'trigger_loop': 2,
                'trigger_mode': '持续',
                'enabled': True,
                'fixed_value': ''
            },
            'total_loops': 5
        },
        {
            'name': '切换模式测试',
            'config': {
                'default_value': 0,
                'trigger_loop': 2,
                'trigger_mode': 'toggle',
                'enabled': True,
                'fixed_value': ''
            },
            'total_loops': 6
        },
        {
            'name': '固定值测试',
            'config': {
                'default_value': 0,
                'trigger_loop': 2,
                'trigger_mode': 'once',
                'enabled': True,
                'fixed_value': '1'
            },
            'total_loops': 5
        },
        {
            'name': '禁用字段测试',
            'config': {
                'default_value': 1,
                'trigger_loop': 1,
                'trigger_mode': 'once',
                'enabled': False,
                'fixed_value': ''
            },
            'total_loops': 5
        },
        {
            'name': '触发循环大于总循环测试',
            'config': {
                'default_value': 0,
                'trigger_loop': 10,
                'trigger_mode': 'once',
                'enabled': True,
                'fixed_value': ''
            },
            'total_loops': 5
        }
    ]
    
    for i, test in enumerate(test_configs):
        print(f"{i+1}. {test['name']}")
        print(f"   配置: {test['config']}")
        print("   结果: ", end="")
        
        field_key = f"test_field_{i}"
        results = []
        
        for loop in range(1, test['total_loops'] + 1):
            value = digital_generator.generate_digital_value(
                field_key, 
                test['config'], 
                loop, 
                test['total_loops']
            )
            results.append(str(value))
        
        print(" -> ".join(results))
        print()
        
        # 重置状态以便下一个测试
        digital_generator.reset_field_states()

def test_random_mode():
    """测试随机模式（多次运行查看随机性）"""
    print("=== 随机模式测试 ===\n")
    
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'random',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("随机模式配置:", config)
    print("多次运行结果:")
    
    for run in range(5):
        digital_generator.reset_field_states()
        field_key = f"random_test_{run}"
        results = []
        
        for loop in range(1, 6):
            value = digital_generator.generate_digital_value(
                field_key, 
                config, 
                loop, 
                5
            )
            results.append(str(value))
        
        print(f"   运行 {run+1}: " + " -> ".join(results))
    
    print()

def test_multiple_fields():
    """测试多个字段的状态管理"""
    print("=== 多字段状态管理测试 ===\n")
    
    fields = {
        'field_A': {
            'default_value': 0,
            'trigger_loop': 2,
            'trigger_mode': 'toggle',
            'enabled': True,
            'fixed_value': ''
        },
        'field_B': {
            'default_value': 1,
            'trigger_loop': 3,
            'trigger_mode': 'toggle',
            'enabled': True,
            'fixed_value': ''
        }
    }
    
    print("字段配置:")
    for field_name, config in fields.items():
        print(f"   {field_name}: {config}")
    
    print("\n循环结果:")
    print("循环  field_A  field_B")
    
    for loop in range(1, 7):
        values = {}
        for field_name, config in fields.items():
            value = digital_generator.generate_digital_value(
                field_name, 
                config, 
                loop, 
                6
            )
            values[field_name] = value
        
        print(f"  {loop}      {values['field_A']}        {values['field_B']}")
    
    print()

if __name__ == "__main__":
    test_digital_logic()
    test_random_mode()
    test_multiple_fields()
    
    print("=== 触发模式说明 ===")
    modes = digital_generator.get_trigger_modes()
    for mode, description in modes.items():
        print(f"- {mode}: {description}")
