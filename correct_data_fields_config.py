"""
基于message.json生成的正确数据字段配置
"""

DATA_FIELDS_CONFIG = {
  "POWER": {
    "description": "电源系统",
    "fields": {
      "SQZDY_TR": {
        "type": "digital",
        "description": "SQZDY_TR",
        "default_value": 0
      },
      "SQBYDY_TR": {
        "type": "digital",
        "description": "SQBYDY_TR",
        "default_value": 0
      },
      "SD_GZ": {
        "type": "digital",
        "description": "SD_GZ",
        "default_value": 0
      },
      "QDDY_GZ": {
        "type": "digital",
        "description": "QDDY_GZ",
        "default_value": 0
      },
      "KZDY_GZ": {
        "type": "digital",
        "description": "KZDY_GZ",
        "default_value": 0
      },
      "QDDY_DY_BJ": {
        "type": "digital",
        "description": "QDDY_DY_BJ",
        "default_value": 0
      },
      "KZDY_DY_BJ": {
        "type": "digital",
        "description": "KZDY_DY_BJ",
        "default_value": 0
      },
      "QDDC_TR_BJ": {
        "type": "digital",
        "description": "QDDC_TR_BJ",
        "default_value": 0
      },
      "KZDC_TR_BJ": {
        "type": "digital",
        "description": "KZDC_TR_BJ",
        "default_value": 0
      },
      "QDDC_DY_BJ": {
        "type": "digital",
        "description": "QDDC_DY_BJ",
        "default_value": 0
      },
      "KZDC_DY_BJ": {
        "type": "digital",
        "description": "KZDC_DY_BJ",
        "default_value": 0
      },
      "XDC_WD_GG_BJ": {
        "type": "digital",
        "description": "XDC_WD_GG_BJ",
        "default_value": 0
      },
      "SX_QDDY_HL1_BJ": {
        "type": "digital",
        "description": "SX_QDDY_HL1_BJ",
        "default_value": 0
      },
      "SX_QDDY_HL2_BJ": {
        "type": "digital",
        "description": "SX_QDDY_HL2_BJ",
        "default_value": 0
      },
      "SX_QDDY_HL3_BJ": {
        "type": "digital",
        "description": "SX_QDDY_HL3_BJ",
        "default_value": 0
      },
      "SX_QDDY_HL4_BJ": {
        "type": "digital",
        "description": "SX_QDDY_HL4_BJ",
        "default_value": 0
      },
      "SX_QDDY_HL5_BJ": {
        "type": "digital",
        "description": "SX_QDDY_HL5_BJ",
        "default_value": 0
      },
      "SX_KZDY_SIG_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_SIG_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_PSL_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_PSL_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_IBP_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_IBP_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_PEDC_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_PEDC_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_DCU_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_DCU_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_AQ_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_AQ_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_JXTC_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_JXTC_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_DKD_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_DKD_HL_BJ",
        "default_value": 0
      },
      "SX_KZDY_LWD_HL_BJ": {
        "type": "digital",
        "description": "SX_KZDY_LWD_HL_BJ",
        "default_value": 0
      },
      "XX_QDDY_HL1_BJ": {
        "type": "digital",
        "description": "XX_QDDY_HL1_BJ",
        "default_value": 0
      },
      "XX_QDDY_HL2_BJ": {
        "type": "digital",
        "description": "XX_QDDY_HL2_BJ",
        "default_value": 0
      },
      "XX_QDDY_HL3_BJ": {
        "type": "digital",
        "description": "XX_QDDY_HL3_BJ",
        "default_value": 0
      },
      "XX_QDDY_HL4_BJ": {
        "type": "digital",
        "description": "XX_QDDY_HL4_BJ",
        "default_value": 0
      },
      "XX_QDDY_HL5_BJ": {
        "type": "digital",
        "description": "XX_QDDY_HL5_BJ",
        "default_value": 0
      },
      "XX_KZDY_SIG_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_SIG_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_PSL_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_PSL_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_IBP_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_IBP_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_PEDC_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_PEDC_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_DCU_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_DCU_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_AQ_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_AQ_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_JXTC_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_JXTC_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_DKD_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_DKD_HL_BJ",
        "default_value": 0
      },
      "XX_KZDY_LWD_HL_BJ": {
        "type": "digital",
        "description": "XX_KZDY_LWD_HL_BJ",
        "default_value": 0
      }
    }
  },
  "PS": {
    "description": "电源监控",
    "fields": {
      "XDC_BM_WD": {
        "type": "analog",
        "description": "XDC_BM_WD",
        "default": [
          58,
          58
        ]
      },
      "XDC_NZ": {
        "type": "analog",
        "description": "XDC_NZ",
        "default": [
          58,
          58
        ]
      },
      "XDC_DY": {
        "type": "analog",
        "description": "XDC_DY",
        "default": [
          58,
          58
        ]
      },
      "QDDY_SC_DL": {
        "type": "analog",
        "description": "QDDY_SC_DL",
        "default": [
          58,
          58
        ]
      },
      "KZDY_SC_DL": {
        "type": "analog",
        "description": "KZDY_SC_DL",
        "default": [
          58,
          58
        ]
      },
      "QDDY_SC_DY": {
        "type": "analog",
        "description": "QDDY_SC_DY",
        "default": [
          58,
          58
        ]
      },
      "KZDY_SC_DY": {
        "type": "analog",
        "description": "KZDY_SC_DY",
        "default": [
          58,
          58
        ]
      }
    }
  },
  "ASD": {
    "description": "自动滑动门",
    "fields": {
      "LCB_GL": {
        "type": "digital",
        "description": "LCB_GL",
        "default_value": 0
      },
      "LCB_ZD": {
        "type": "digital",
        "description": "LCB_ZD",
        "default_value": 0
      },
      "LCB_JDKM": {
        "type": "digital",
        "description": "LCB_JDKM",
        "default_value": 0
      },
      "LCB_JDGM": {
        "type": "digital",
        "description": "LCB_JDGM",
        "default_value": 0
      },
      "HDM_ZKDW": {
        "type": "digital",
        "description": "HDM_ZKDW",
        "default_value": 0
      },
      "HDM_YKDW": {
        "type": "digital",
        "description": "HDM_YKDW",
        "default_value": 0
      },
      "HDM_ZGDW": {
        "type": "digital",
        "description": "HDM_ZGDW",
        "default_value": 0
      },
      "HDM_YGDW": {
        "type": "digital",
        "description": "HDM_YGDW",
        "default_value": 0
      },
      "YC_KM": {
        "type": "digital",
        "description": "YC_KM",
        "default_value": 0
      },
      "YC_GM": {
        "type": "digital",
        "description": "YC_GM",
        "default_value": 0
      },
      "DWGL_ZT": {
        "type": "digital",
        "description": "DWGL_ZT",
        "default_value": 0
      },
      "SDJS_BJ": {
        "type": "digital",
        "description": "SDJS_BJ",
        "default_value": 0
      },
      "ZAWTC_BJ": {
        "type": "digital",
        "description": "ZAWTC_BJ",
        "default_value": 0
      },
      "DCSJSYC_BJ": {
        "type": "digital",
        "description": "DCSJSYC_BJ",
        "default_value": 0
      },
      "DCU_GZ": {
        "type": "digital",
        "description": "DCU_GZ",
        "default_value": 0
      },
      "ZXWKG_GZ": {
        "type": "digital",
        "description": "ZXWKG_GZ",
        "default_value": 0
      },
      "YXWKG_GZ": {
        "type": "digital",
        "description": "YXWKG_GZ",
        "default_value": 0
      },
      "DJ_GZ": {
        "type": "digital",
        "description": "DJ_GZ",
        "default_value": 0
      },
      "GM_GZ": {
        "type": "digital",
        "description": "GM_GZ",
        "default_value": 0
      },
      "KM_GZ": {
        "type": "digital",
        "description": "KM_GZ",
        "default_value": 0
      },
      "DJ_DL": {
        "type": "analog",
        "description": "DJ_DL",
        "default": [
          58,
          58
        ]
      },
      "DJ_SD": {
        "type": "analog",
        "description": "DJ_SD",
        "default": [
          58,
          58
        ]
      },
      "DJ_SCNJ": {
        "type": "analog",
        "description": "DJ_SCNJ",
        "default": [
          58,
          58
        ]
      },
      "QDB_DY": {
        "type": "analog",
        "description": "QDB_DY",
        "default": [
          58,
          58
        ]
      },
      "YC_KM_ML_DY": {
        "type": "analog",
        "description": "YC_KM_ML_DY",
        "default": [
          58,
          58
        ]
      },
      "YC_GM_ML_DY": {
        "type": "analog",
        "description": "YC_GM_ML_DY",
        "default": [
          58,
          58
        ]
      }
    }
  },
  "EED": {
    "description": "紧急疏散门",
    "fields": {
      "YJM_DK": {
        "type": "digital",
        "description": "YJM_DK",
        "default_value": 0
      },
      "YJM_GB": {
        "type": "digital",
        "description": "YJM_GB",
        "default_value": 0
      },
      "YJM_PL": {
        "type": "digital",
        "description": "YJM_PL",
        "default_value": 0
      }
    }
  },
  "IBP": {
    "description": "综合后备盘",
    "fields": {
      "IBP_SNXX": {
        "type": "digital",
        "description": "IBP_SNXX",
        "default_value": 0
      },
      "IBP_SQ_PSL": {
        "type": "digital",
        "description": "IBP_SQ_PSL",
        "default_value": 0
      },
      "IBP_KM_ML": {
        "type": "digital",
        "description": "IBP_KM_ML",
        "default_value": 0
      },
      "IBP_GM_ML": {
        "type": "digital",
        "description": "IBP_GM_ML",
        "default_value": 0
      }
    }
  },
  "MSD": {
    "description": "手动滑动门",
    "fields": {
      "CT_DM_DK": {
        "type": "digital",
        "description": "CT_DM_DK",
        "default_value": 0
      },
      "CT_DM_GB": {
        "type": "digital",
        "description": "CT_DM_GB",
        "default_value": 0
      },
      "CT_DM_DK_CS_BJ": {
        "type": "digital",
        "description": "CT_DM_DK_CS_BJ",
        "default_value": 0
      },
      "CT_DM_PL": {
        "type": "digital",
        "description": "CT_DM_PL",
        "default_value": 0
      },
      "CW_DM_DK": {
        "type": "digital",
        "description": "CW_DM_DK",
        "default_value": 0
      },
      "CW_DM_GB": {
        "type": "digital",
        "description": "CW_DM_GB",
        "default_value": 0
      },
      "CW_DM_DK_CS_BJ": {
        "type": "digital",
        "description": "CW_DM_DK_CS_BJ",
        "default_value": 0
      },
      "CW_DM_PL": {
        "type": "digital",
        "description": "CW_DM_PL",
        "default_value": 0
      }
    }
  },
  "PDS": {
    "description": "乘客检测系统",
    "fields": {
      "JXTC_BJ": {
        "type": "digital",
        "description": "JXTC_BJ",
        "default_value": 0
      },
      "JXTC_PL": {
        "type": "digital",
        "description": "JXTC_PL",
        "default_value": 0
      },
      "JXTC_GZ": {
        "type": "digital",
        "description": "JXTC_GZ",
        "default_value": 0
      }
    }
  },
  "PEDC": {
    "description": "乘客紧急对讲",
    "fields": {
      "JKXT_PEDC_GZ": {
        "type": "digital",
        "description": "JKXT_PEDC_GZ",
        "default_value": 0
      },
      "XCZX_GZ": {
        "type": "digital",
        "description": "XCZX_GZ",
        "default_value": 0
      },
      "ZC_HDM_GMSJ_XH_DY": {
        "type": "analog",
        "description": "ZC_HDM_GMSJ_XH_DY",
        "default": [
          58,
          58
        ]
      },
      "ZC_YJM_GBSJ_XH_DY": {
        "type": "analog",
        "description": "ZC_YJM_GBSJ_XH_DY",
        "default": [
          58,
          58
        ]
      },
      "ZC_JXTC_XT_AQ_XH_DY": {
        "type": "analog",
        "description": "ZC_JXTC_XT_AQ_XH_DY",
        "default": [
          58,
          58
        ]
      },
      "KM_ML_SCJDQ_JC": {
        "type": "analog",
        "description": "KM_ML_SCJDQ_JC",
        "default": [
          58,
          58
        ]
      },
      "GM_ML_SCJDQ_JC": {
        "type": "analog",
        "description": "GM_ML_SCJDQ_JC",
        "default": [
          58,
          58
        ]
      }
    }
  },
  "PSL": {
    "description": "站台安全门",
    "fields": {
      "PSL_SN": {
        "type": "digital",
        "description": "PSL_SN",
        "default_value": 0
      },
      "PSL_KM": {
        "type": "digital",
        "description": "PSL_KM",
        "default_value": 0
      },
      "PSL_GM": {
        "type": "digital",
        "description": "PSL_GM",
        "default_value": 0
      },
      "PSL_HSJC": {
        "type": "digital",
        "description": "PSL_HSJC",
        "default_value": 0
      }
    }
  },
  "SIG": {
    "description": "信号系统",
    "fields": {
      "XH_KM_ML": {
        "type": "digital",
        "description": "XH_KM_ML",
        "default_value": 0
      },
      "XH_GM_ML": {
        "type": "digital",
        "description": "XH_GM_ML",
        "default_value": 0
      },
      "ZW_XH": {
        "type": "digital",
        "description": "ZW_XH",
        "default_value": 0
      },
      "HS_JC": {
        "type": "digital",
        "description": "HS_JC",
        "default_value": 0
      },
      "GB_SJ": {
        "type": "digital",
        "description": "GB_SJ",
        "default_value": 0
      },
      "PSD_GD_DY": {
        "type": "analog",
        "description": "PSD_GD_DY",
        "default": [
          58,
          58
        ]
      },
      "ZW_XH_DY": {
        "type": "analog",
        "description": "ZW_XH_DY",
        "default": [
          58,
          58
        ]
      },
      "KM_ML_DY": {
        "type": "analog",
        "description": "KM_ML_DY",
        "default": [
          58,
          58
        ]
      },
      "GM_ML_DY": {
        "type": "analog",
        "description": "GM_ML_DY",
        "default": [
          58,
          58
        ]
      },
      "SIG_GD_DY": {
        "type": "analog",
        "description": "SIG_GD_DY",
        "default": [
          58,
          58
        ]
      },
      "ZCGB_SJXH_DY": {
        "type": "analog",
        "description": "ZCGB_SJXH_DY",
        "default": [
          58,
          58
        ]
      },
      "HSJCXH_DY": {
        "type": "analog",
        "description": "HSJCXH_DY",
        "default": [
          58,
          58
        ]
      }
    }
  }
}