#!/usr/bin/env python3
"""
测试GUI启动脚本
"""
import sys
import traceback

def test_imports():
    """测试所有必要的导入"""
    print("测试导入模块...")
    
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
    except Exception as e:
        print(f"✗ tkinter 导入失败: {e}")
        return False
    
    try:
        from tkinter import ttk, messagebox, filedialog
        print("✓ tkinter 子模块导入成功")
    except Exception as e:
        print(f"✗ tkinter 子模块导入失败: {e}")
        return False
    
    try:
        import paho.mqtt.client as mqtt
        print("✓ paho.mqtt 导入成功")
    except Exception as e:
        print(f"✗ paho.mqtt 导入失败: {e}")
        return False
    
    try:
        from data_generator import StationDataGenerator
        print("✓ data_generator 导入成功")
    except Exception as e:
        print(f"✗ data_generator 导入失败: {e}")
        return False
    
    try:
        from data_fields_config import DATA_FIELDS_CONFIG
        print("✓ data_fields_config 导入成功")
    except Exception as e:
        print(f"✗ data_fields_config 导入失败: {e}")
        return False
    
    return True

def test_gui_creation():
    """测试GUI创建"""
    print("\n测试GUI创建...")
    
    try:
        import tkinter as tk
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        # 创建一个简单的标签
        label = tk.Label(root, text="GUI测试成功！")
        label.pack(pady=50)
        
        # 创建关闭按钮
        def close_window():
            print("关闭测试窗口")
            root.destroy()
        
        close_btn = tk.Button(root, text="关闭", command=close_window)
        close_btn.pack(pady=10)
        
        print("✓ 测试窗口创建成功")
        print("显示窗口3秒后自动关闭...")
        
        # 3秒后自动关闭
        root.after(3000, close_window)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI创建失败: {e}")
        traceback.print_exc()
        return False

def test_main_app():
    """测试主应用程序"""
    print("\n测试主应用程序...")
    
    try:
        from multi_station_gui import MultiStationMQTTGenerator
        import tkinter as tk
        
        root = tk.Tk()
        app = MultiStationMQTTGenerator(root)
        
        print("✓ 主应用程序创建成功")
        print("显示主窗口5秒后自动关闭...")
        
        # 5秒后自动关闭
        def close_app():
            print("关闭主应用程序")
            root.destroy()
        
        root.after(5000, close_app)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ 主应用程序创建失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== GUI启动测试 ===")
    
    # 测试导入
    if not test_imports():
        print("\n导入测试失败，退出")
        sys.exit(1)
    
    # 测试简单GUI
    if not test_gui_creation():
        print("\n简单GUI测试失败，退出")
        sys.exit(1)
    
    # 测试主应用程序
    if not test_main_app():
        print("\n主应用程序测试失败，退出")
        sys.exit(1)
    
    print("\n=== 所有测试通过 ===")
