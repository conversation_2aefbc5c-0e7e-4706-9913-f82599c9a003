#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Simple system check for Python 2.7 compatibility
"""
import sys
import os

def main():
    print("=== Simple System Check ===")
    print("Python version: " + str(sys.version))
    print("Python executable: " + str(sys.executable))
    print("Working directory: " + str(os.getcwd()))
    print("Platform: " + str(sys.platform))
    
    print("\n=== Basic Module Check ===")
    
    # Check basic modules
    modules = ['json', 'time', 'threading', 'logging', 'random']
    
    for module in modules:
        try:
            __import__(module)
            print("OK: " + module)
        except ImportError as e:
            print("ERROR: " + module + " - " + str(e))
    
    print("\n=== GUI Check ===")
    try:
        # For Python 2, tkinter is called Tkinter
        if sys.version_info[0] == 2:
            import Tkinter as tk
        else:
            import tkinter as tk
        print("OK: GUI module imported")
        
        # Try to create a simple window
        root = tk.Tk()
        root.withdraw()  # Hide the window
        print("OK: GUI window created")
        root.destroy()
        print("OK: GUI window destroyed")
        
    except Exception as e:
        print("ERROR: GUI - " + str(e))
    
    print("\n=== File Check ===")
    files = ['multi_station_gui.py', 'data_generator.py', 'data_fields_config.py']
    
    for filename in files:
        if os.path.exists(filename):
            print("OK: " + filename + " exists")
        else:
            print("ERROR: " + filename + " not found")
    
    print("\n=== Check Complete ===")

if __name__ == "__main__":
    main()
