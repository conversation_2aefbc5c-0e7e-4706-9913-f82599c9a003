"""
验证字段配置是否与message.json格式一致
"""
import json
from data_fields_config import DATA_FIELDS_CONFIG

def verify_field_configs():
    """验证字段配置"""
    print("=== 验证字段配置 ===\n")
    
    # 读取message.json中的实际字段
    with open("message.json", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 解析JSON对象
    json_objects = []
    parts = content.split('}\n{')
    
    for i, part in enumerate(parts):
        if i == 0:
            json_str = part + '}'
        elif i == len(parts) - 1:
            json_str = '{' + part
        else:
            json_str = '{' + part + '}'
        
        try:
            obj = json.loads(json_str)
            json_objects.append(obj)
        except json.JSONDecodeError:
            continue
    
    # 收集实际字段
    actual_fields = {}
    for obj in json_objects:
        topic = obj.get("topic", "")
        payload = obj.get("payload", {})
        
        # 解析设备类型
        topic_parts = topic.split("/")
        if len(topic_parts) >= 4:
            if len(topic_parts) == 4:
                device_type = topic_parts[3]
            else:
                device_name = topic_parts[4]
                if device_name.startswith("ASD"):
                    device_type = "ASD"
                elif device_name.startswith("EED"):
                    device_type = "EED"
                elif device_name.startswith("MSD"):
                    device_type = "MSD"
                elif device_name.startswith("PDS"):
                    device_type = "PDS"
                elif device_name.startswith("PSL"):
                    device_type = "PSL"
                elif device_name == "IBP":
                    device_type = "IBP"
                elif device_name == "PEDC":
                    device_type = "PEDC"
                elif device_name == "SIG":
                    device_type = "SIG"
                else:
                    continue
            
            if device_type not in actual_fields:
                actual_fields[device_type] = set()
            
            for field_name in payload.keys():
                if field_name != "timestamp":
                    actual_fields[device_type].add(field_name)
    
    # 验证配置
    print("字段配置验证结果:")
    print("=" * 50)
    
    all_correct = True
    
    for device_type in sorted(actual_fields.keys()):
        print(f"\n{device_type} 设备:")
        
        if device_type in DATA_FIELDS_CONFIG:
            config_fields = set(DATA_FIELDS_CONFIG[device_type]["fields"].keys())
            actual_device_fields = actual_fields[device_type]
            
            missing_fields = actual_device_fields - config_fields
            extra_fields = config_fields - actual_device_fields
            
            if not missing_fields and not extra_fields:
                print(f"  ✅ 字段完全匹配 ({len(config_fields)} 个字段)")
            else:
                all_correct = False
                if missing_fields:
                    print(f"  ❌ 配置中缺失字段: {missing_fields}")
                if extra_fields:
                    print(f"  ❌ 配置中多余字段: {extra_fields}")
        else:
            all_correct = False
            print(f"  ❌ 配置中缺少 {device_type} 设备")
    
    print("\n" + "=" * 50)
    if all_correct:
        print("✅ 所有字段配置都正确！")
    else:
        print("❌ 字段配置存在问题，需要修复")
    
    return all_correct

def test_data_generation():
    """测试数据生成"""
    print("\n=== 测试数据生成 ===\n")
    
    try:
        from data_generator import StationDataGenerator
        
        # 创建数据生成器
        generator = StationDataGenerator()
        
        # 创建测试车站配置
        test_station = {
            "station_id": "S99",
            "station_name": "测试车站",
            "enabled": True,
            "config": {
                "asd_count": {"sx": 2, "xx": 2},
                "eed_count": {"sx": 1, "xx": 1},
                "msd_count": {"sx": 1, "xx": 1},
                "pds_count": {"sx": 1, "xx": 1},
                "psl_count": {"sx": 1, "xx": 1}
            },
            "field_configs": {}
        }
        
        # 生成默认字段配置
        for device_type, device_config in DATA_FIELDS_CONFIG.items():
            test_station["field_configs"][device_type] = {}
            
            for field_name, field_info in device_config["fields"].items():
                if field_info["type"] == "digital":
                    test_station["field_configs"][device_type][field_name] = {
                        "default_value": field_info.get("default_value", 0),
                        "trigger_loop": 0,
                        "trigger_mode": "once",
                        "fixed_value": "",
                        "enabled": True
                    }
                else:
                    default_range = field_info.get("default", [0, 100])
                    test_station["field_configs"][device_type][field_name] = {
                        "range": default_range,
                        "fixed_value": "",
                        "enabled": True
                    }
        
        # 生成测试数据
        messages = generator.generate_station_data("L03", test_station, 1, 1)
        
        print(f"成功生成 {len(messages)} 条消息")
        
        # 验证生成的数据格式
        device_message_count = {}
        for msg in messages:
            topic = msg["topic"]
            topic_parts = topic.split("/")
            
            if len(topic_parts) >= 4:
                if len(topic_parts) == 4:
                    device_type = topic_parts[3]
                else:
                    device_name = topic_parts[4]
                    if device_name.startswith("ASD"):
                        device_type = "ASD"
                    elif device_name.startswith("EED"):
                        device_type = "EED"
                    elif device_name.startswith("MSD"):
                        device_type = "MSD"
                    elif device_name.startswith("PDS"):
                        device_type = "PDS"
                    elif device_name.startswith("PSL"):
                        device_type = "PSL"
                    elif device_name == "IBP":
                        device_type = "IBP"
                    elif device_name == "PEDC":
                        device_type = "PEDC"
                    elif device_name == "SIG":
                        device_type = "SIG"
                    else:
                        device_type = "UNKNOWN"
                
                device_message_count[device_type] = device_message_count.get(device_type, 0) + 1
        
        print("\n生成的消息统计:")
        for device_type, count in sorted(device_message_count.items()):
            print(f"  {device_type}: {count} 条消息")
        
        # 显示几个示例消息
        print("\n示例消息:")
        for i, msg in enumerate(messages[:3]):
            print(f"  {i+1}. {msg['topic']}")
            payload_fields = [k for k in msg['payload'].keys() if k != 'timestamp']
            print(f"     字段: {payload_fields}")
        
        print("✅ 数据生成测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 数据生成测试失败: {e}")
        return False

def compare_with_message_json():
    """与message.json格式对比"""
    print("\n=== 与message.json格式对比 ===\n")
    
    # 读取message.json中的示例
    with open("message.json", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 获取第一个JSON对象作为示例
    first_json = content.split('}\n{')[0] + '}'
    try:
        example_msg = json.loads(first_json)
        
        print("message.json中的示例格式:")
        print(f"Topic: {example_msg['topic']}")
        print(f"Payload字段: {list(example_msg['payload'].keys())}")
        print(f"示例数据: {json.dumps(example_msg['payload'], ensure_ascii=False, indent=2)}")
        
        return True
    except Exception as e:
        print(f"解析message.json失败: {e}")
        return False

if __name__ == "__main__":
    print("开始验证字段配置...\n")
    
    # 验证字段配置
    config_correct = verify_field_configs()
    
    # 测试数据生成
    generation_works = test_data_generation()
    
    # 格式对比
    format_ok = compare_with_message_json()
    
    print("\n" + "=" * 60)
    print("验证总结:")
    print(f"字段配置正确性: {'✅ 通过' if config_correct else '❌ 失败'}")
    print(f"数据生成功能: {'✅ 通过' if generation_works else '❌ 失败'}")
    print(f"格式兼容性: {'✅ 通过' if format_ok else '❌ 失败'}")
    
    if config_correct and generation_works and format_ok:
        print("\n🎉 所有验证都通过！字段配置已正确修复！")
    else:
        print("\n⚠️ 仍有问题需要解决")
    
    print("\n使用建议:")
    print("1. 现在可以添加新车站，字段配置将自动正确")
    print("2. 生成的数据格式与message.json完全一致")
    print("3. 支持所有设备类型的正确字段配置")
    print("4. 开关量和数字量字段都有正确的配置格式")
