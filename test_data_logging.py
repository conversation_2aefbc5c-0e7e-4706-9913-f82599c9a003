"""
测试发送数据日志功能
"""
import json
import time
from data_generator import StationDataGenerator

def test_data_logging():
    """测试数据日志功能"""
    print("=== 测试发送数据日志功能 ===\n")
    
    # 创建数据生成器
    generator = StationDataGenerator()
    
    # 创建测试车站配置
    test_station = {
        "station_id": "S18",
        "station_name": "测试车站",
        "enabled": True,
        "config": {
            "asd_count": {"sx": 2, "xx": 2},
            "eed_count": {"sx": 1, "xx": 1},
            "msd_count": {"sx": 1, "xx": 1},
            "pds_count": {"sx": 1, "xx": 1},
            "psl_count": {"sx": 1, "xx": 1}
        },
        "field_configs": {}
    }
    
    # 生成默认字段配置
    from data_fields_config import DATA_FIELDS_CONFIG
    
    for device_type, device_config in DATA_FIELDS_CONFIG.items():
        test_station["field_configs"][device_type] = {}
        
        for field_name, field_info in device_config["fields"].items():
            if field_info["type"] == "digital":
                test_station["field_configs"][device_type][field_name] = {
                    "default_value": field_info.get("default_value", 0),
                    "trigger_loop": 0,
                    "trigger_mode": "once",
                    "fixed_value": "",
                    "enabled": True
                }
            else:
                default_range = field_info.get("default", [0, 100])
                test_station["field_configs"][device_type][field_name] = {
                    "range": default_range,
                    "fixed_value": "",
                    "enabled": True
                }
    
    # 生成测试数据
    messages = generator.generate_station_data("L03", test_station, 1, 1)
    
    print(f"生成了 {len(messages)} 条消息\n")
    
    # 模拟日志记录功能
    def mock_log_data_content(station_id, topic, payload, current_loop):
        """模拟日志数据内容记录"""
        # 获取设备类型
        topic_parts = topic.split("/")
        if len(topic_parts) >= 4:
            if len(topic_parts) == 4:
                device_name = topic_parts[3]
            else:
                device_name = topic_parts[4]
        else:
            device_name = "UNKNOWN"
        
        # 过滤掉timestamp字段
        data_fields = {k: v for k, v in payload.items() if k != "timestamp"}
        
        # 格式化数据内容
        if len(data_fields) <= 5:
            # 字段较少时显示所有字段
            fields_str = ", ".join([f"{k}={v}" for k, v in data_fields.items()])
        else:
            # 字段较多时只显示前几个字段
            first_fields = list(data_fields.items())[:3]
            fields_str = ", ".join([f"{k}={v}" for k, v in first_fields])
            fields_str += f", ... (共{len(data_fields)}个字段)"
        
        # 记录日志
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] 循环{current_loop} - {station_id}/{device_name}: {fields_str}"
        print(log_message)
    
    # 测试不同类型的设备日志
    print("=== 发送数据日志示例 ===\n")
    
    device_types = {}
    for msg in messages:
        topic = msg["topic"]
        topic_parts = topic.split("/")
        if len(topic_parts) >= 4:
            if len(topic_parts) == 4:
                device_type = topic_parts[3]
            else:
                device_name = topic_parts[4]
                if device_name.startswith("ASD"):
                    device_type = "ASD"
                elif device_name.startswith("EED"):
                    device_type = "EED"
                elif device_name.startswith("MSD"):
                    device_type = "MSD"
                elif device_name.startswith("PDS"):
                    device_type = "PDS"
                elif device_name.startswith("PSL"):
                    device_type = "PSL"
                elif device_name == "IBP":
                    device_type = "IBP"
                elif device_name == "PEDC":
                    device_type = "PEDC"
                elif device_name == "SIG":
                    device_type = "SIG"
                else:
                    device_type = device_name
            
            if device_type not in device_types:
                device_types[device_type] = msg
    
    # 显示每种设备类型的日志示例
    for device_type, msg in device_types.items():
        print(f"{device_type} 设备日志示例:")
        mock_log_data_content("S18", msg["topic"], msg["payload"], 1)
        print()

def test_log_formatting():
    """测试不同数据量的日志格式"""
    print("=== 测试日志格式 ===\n")
    
    # 测试少量字段的设备
    small_payload = {
        "timestamp": 1640000000000,
        "YJM_DK": 0,
        "YJM_GB": 1,
        "YJM_PL": 0
    }
    
    # 测试大量字段的设备
    large_payload = {
        "timestamp": 1640000000000,
        "SQZDY_TR": 0,
        "SQBYDY_TR": 1,
        "SD_GZ": 0,
        "QDDY_GZ": 1,
        "KZDY_GZ": 1,
        "QDDY_DY_BJ": 1,
        "KZDY_DY_BJ": 0,
        "QDDC_TR_BJ": 0,
        "KZDC_TR_BJ": 0,
        "QDDC_DY_BJ": 0
    }
    
    def format_log_content(payload, device_name):
        """格式化日志内容"""
        data_fields = {k: v for k, v in payload.items() if k != "timestamp"}
        
        if len(data_fields) <= 5:
            fields_str = ", ".join([f"{k}={v}" for k, v in data_fields.items()])
        else:
            first_fields = list(data_fields.items())[:3]
            fields_str = ", ".join([f"{k}={v}" for k, v in first_fields])
            fields_str += f", ... (共{len(data_fields)}个字段)"
        
        timestamp = time.strftime("%H:%M:%S")
        return f"[{timestamp}] 循环1 - S18/{device_name}: {fields_str}"
    
    print("少量字段设备 (EED):")
    print(format_log_content(small_payload, "EED01"))
    print()
    
    print("大量字段设备 (POWER):")
    print(format_log_content(large_payload, "POWER"))
    print()

def test_toggle_field_logging():
    """测试toggle字段的日志记录"""
    print("=== 测试Toggle字段日志 ===\n")
    
    # 模拟HDM_YGDW字段的toggle变化
    toggle_configs = [
        {"HDM_YGDW": 0, "HDM_ZKDW": 1, "LCB_GL": 0},
        {"HDM_YGDW": 0, "HDM_ZKDW": 1, "LCB_GL": 0},
        {"HDM_YGDW": 1, "HDM_ZKDW": 0, "LCB_GL": 1},
        {"HDM_YGDW": 1, "HDM_ZKDW": 0, "LCB_GL": 1},
        {"HDM_YGDW": 0, "HDM_ZKDW": 1, "LCB_GL": 0}
    ]
    
    print("ASD设备Toggle字段变化日志:")
    for i, config in enumerate(toggle_configs, 1):
        payload = {"timestamp": 1640000000000 + i * 1000}
        payload.update(config)
        
        data_fields = {k: v for k, v in payload.items() if k != "timestamp"}
        fields_str = ", ".join([f"{k}={v}" for k, v in data_fields.items()])
        
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] 循环{i} - S18/ASD01: {fields_str}"
        print(log_message)
        
        time.sleep(0.1)  # 模拟时间间隔
    
    print()

def generate_log_config_example():
    """生成日志配置示例"""
    print("=== 日志配置示例 ===\n")
    
    log_config = {
        "global_settings": {
            "show_data_content": True,  # 是否显示发送数据内容
            "log_level": "INFO",        # 日志级别
            "max_log_lines": 1000       # 最大日志行数
        },
        "log_format": {
            "show_timestamp": True,     # 显示时间戳
            "show_loop_number": True,   # 显示循环次数
            "show_station_id": True,    # 显示车站ID
            "show_device_name": True,   # 显示设备名称
            "max_fields_display": 3     # 最多显示字段数
        }
    }
    
    print("推荐的日志配置:")
    print(json.dumps(log_config, ensure_ascii=False, indent=2))
    print()

if __name__ == "__main__":
    test_data_logging()
    test_log_formatting()
    test_toggle_field_logging()
    generate_log_config_example()
    
    print("=== 使用说明 ===")
    print("1. 在应用界面中勾选'显示发送数据内容'来启用详细日志")
    print("2. 日志会显示每个循环中每个设备发送的数据内容")
    print("3. 对于字段较多的设备，只显示前3个字段和总数")
    print("4. 可以通过日志监控toggle字段的变化")
    print("5. 日志格式: [时间] 循环X - 车站ID/设备名: 字段1=值1, 字段2=值2, ...")
    print("6. 取消勾选可以关闭详细日志，只显示基本的发送统计信息")
