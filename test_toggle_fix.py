"""
测试toggle模式修复后的效果
"""
from digital_field_generator import digital_generator

def test_toggle_scenarios():
    """测试各种toggle场景"""
    print("=== Toggle模式测试 ===\n")
    
    scenarios = [
        {
            'name': 'HDM_YGDW - 默认值0，第2次循环开始切换',
            'config': {
                'default_value': 0,
                'trigger_loop': 2,
                'trigger_mode': 'toggle',
                'enabled': True,
                'fixed_value': ''
            },
            'loops': 8
        },
        {
            'name': 'HDM_YGDW - 默认值1，第1次循环开始切换',
            'config': {
                'default_value': 1,
                'trigger_loop': 1,
                'trigger_mode': 'toggle',
                'enabled': True,
                'fixed_value': ''
            },
            'loops': 6
        },
        {
            'name': 'HDM_YGDW - 默认值0，第3次循环开始切换',
            'config': {
                'default_value': 0,
                'trigger_loop': 3,
                'trigger_mode': 'toggle',
                'enabled': True,
                'fixed_value': ''
            },
            'loops': 7
        }
    ]
    
    for i, scenario in enumerate(scenarios):
        print(f"{i+1}. {scenario['name']}")
        print(f"   配置: 默认值={scenario['config']['default_value']}, 触发循环={scenario['config']['trigger_loop']}")
        
        # 重置状态
        digital_generator.reset_field_states()
        
        field_key = f"HDM_YGDW_{i}"
        results = []
        
        for loop in range(1, scenario['loops'] + 1):
            value = digital_generator.generate_digital_value(
                field_key,
                scenario['config'],
                loop,
                scenario['loops']
            )
            results.append(str(value))
        
        print(f"   结果: {' -> '.join(results)}")
        print()

def test_multiple_runs():
    """测试多次运行的一致性"""
    print("=== 多次运行一致性测试 ===\n")
    
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("配置: 默认值=0, 触发循环=2, 模式=toggle")
    print("多次运行结果（每次都重置状态）:")
    
    for run in range(3):
        # 重置状态（模拟重新开始发送）
        digital_generator.reset_field_states()
        
        field_key = "HDM_YGDW_test"
        results = []
        
        for loop in range(1, 6):
            value = digital_generator.generate_digital_value(
                field_key,
                config,
                loop,
                5
            )
            results.append(str(value))
        
        print(f"   运行 {run+1}: {' -> '.join(results)}")
    
    print()

def test_without_reset():
    """测试不重置状态的情况"""
    print("=== 不重置状态测试 ===\n")
    
    config = {
        'default_value': 0,
        'trigger_loop': 1,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("配置: 默认值=0, 触发循环=1, 模式=toggle")
    print("连续运行（不重置状态）:")
    
    field_key = "HDM_YGDW_continuous"
    
    for run in range(3):
        results = []
        
        for loop in range(1, 4):
            value = digital_generator.generate_digital_value(
                field_key,
                config,
                loop,
                3
            )
            results.append(str(value))
        
        print(f"   运行 {run+1}: {' -> '.join(results)}")
    
    print()

if __name__ == "__main__":
    test_toggle_scenarios()
    test_multiple_runs()
    test_without_reset()
    
    print("=== 使用建议 ===")
    print("1. 每次开始新的数据发送时，应该调用 digital_generator.reset_field_states()")
    print("2. Toggle模式会从触发循环开始每次切换状态")
    print("3. 如果看不到切换效果，请检查:")
    print("   - 触发循环是否设置正确")
    print("   - 总循环次数是否足够")
    print("   - 是否在每次发送前重置了状态")
