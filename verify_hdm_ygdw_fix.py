"""
验证HDM_YGDW字段的toggle修复
模拟实际运行环境
"""
from digital_field_generator import digital_generator
import json

def simulate_actual_run():
    """模拟实际运行环境"""
    print("=== 模拟实际运行环境 ===\n")
    
    # 模拟您的实际配置
    station_config = {
        "station_id": "S18",
        "field_configs": {
            "ASD": {
                "HDM_YGDW": {
                    "default_value": 0,
                    "trigger_loop": 2,
                    "trigger_mode": "toggle",
                    "fixed_value": "",
                    "enabled": True
                }
            }
        }
    }
    
    # 模拟10个循环（您的配置）
    total_loops = 10
    
    print(f"车站: {station_config['station_id']}")
    print(f"HDM_YGDW配置: {station_config['field_configs']['ASD']['HDM_YGDW']}")
    print(f"总循环次数: {total_loops}")
    print()
    
    # 重置状态（模拟开始发送）
    digital_generator.reset_field_states()
    
    # 模拟多个ASD设备
    directions = ["SX", "XX"]
    asd_devices = [1, 2, 3]  # 测试前3个设备
    
    print("模拟ASD设备的HDM_YGDW字段:")
    print("=" * 60)
    
    for direction in directions:
        print(f"\n{direction}方向:")
        for device_num in asd_devices:
            field_key = f"{station_config['station_id']}_{direction}_ASD{device_num:02d}_HDM_YGDW"
            
            print(f"ASD{device_num:02d}: ", end="")
            values = []
            
            for loop in range(1, total_loops + 1):
                value = digital_generator.generate_digital_value(
                    field_key,
                    station_config['field_configs']['ASD']['HDM_YGDW'],
                    loop,
                    total_loops
                )
                values.append(str(value))
            
            print(" -> ".join(values))
            
            # 验证是否正确切换
            expected_pattern = ['0', '1', '0', '1', '0', '1', '0', '1', '0', '1']
            if values == expected_pattern:
                print("       ✅ 正确切换")
            else:
                print("       ❌ 切换异常")
                print(f"       预期: {' -> '.join(expected_pattern)}")

def test_problem_scenario():
    """测试之前的问题场景"""
    print("\n=== 测试之前的问题场景 ===\n")
    
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    print("问题描述: 第一次发0，其余都发1")
    print("修复前预期错误结果: 0 -> 1 -> 1 -> 1 -> 1 -> ...")
    print("修复后正确结果:     0 -> 1 -> 0 -> 1 -> 0 -> ...")
    print()
    
    # 重置状态
    digital_generator.reset_field_states()
    
    field_key = "HDM_YGDW_problem_test"
    
    print("修复后实际结果:")
    for loop in range(1, 11):
        value = digital_generator.generate_digital_value(
            field_key,
            config,
            loop,
            10
        )
        print(f"循环 {loop:2d}: {value}")
    
    print("\n✅ 问题已修复！现在能正确地在0和1之间切换")

def create_test_message():
    """创建测试消息，展示修复后的效果"""
    print("\n=== 生成测试消息 ===\n")
    
    # 重置状态
    digital_generator.reset_field_states()
    
    config = {
        'default_value': 0,
        'trigger_loop': 2,
        'trigger_mode': 'toggle',
        'enabled': True,
        'fixed_value': ''
    }
    
    # 生成3个循环的消息
    messages = []
    for loop in range(1, 4):
        hdm_ygdw_value = digital_generator.generate_digital_value(
            "S18_SX_ASD01_HDM_YGDW",
            config,
            loop,
            3
        )
        
        message = {
            "timestamp": 1640000000000 + loop * 1000,
            "HDM_YGDW": hdm_ygdw_value,
            "HDM_ZKDW": 1,  # 其他字段示例
            "LCB_GL": 1
        }
        messages.append(message)
    
    print("生成的测试消息（L03/S18/PSD/SX/ASD01）:")
    for i, msg in enumerate(messages, 1):
        print(f"循环 {i}: {json.dumps(msg, indent=2)}")
    
    print(f"\nHDM_YGDW变化: {' -> '.join([str(msg['HDM_YGDW']) for msg in messages])}")

if __name__ == "__main__":
    simulate_actual_run()
    test_problem_scenario()
    create_test_message()
    
    print("\n=== 使用说明 ===")
    print("1. 重新启动应用程序以应用修复")
    print("2. 配置HDM_YGDW字段: 默认值=0, 触发循环=2, 模式=toggle")
    print("3. 开始发送数据，观察HDM_YGDW字段的变化")
    print("4. 现在应该看到: 0 -> 1 -> 0 -> 1 -> 0 -> 1 -> ...")
    print("5. 而不是之前的: 0 -> 1 -> 1 -> 1 -> 1 -> 1 -> ...")
