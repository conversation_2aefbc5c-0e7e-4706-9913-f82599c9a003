"""
数据字段配置定义
基于main.py中的具体数据字段，提供详细的配置选项
"""

# 数据字段定义，包含字段名称、类型、默认范围和描述
DATA_FIELDS_CONFIG = {
    "POWER": {
        "topic_suffix": "POWER",
        "description": "电源数据",
        "fields": {
            "SQZDY_TR": {"type": "digital", "default_value": 0, "description": "上桥整流器投入"},
            "SQBYDY_TR": {"type": "digital", "default_value": 0, "description": "上桥备用电源投入"},
            "SD_GZ": {"type": "digital", "default_value": 0, "description": "手动故障"},
            "QDDY_GZ": {"type": "digital", "default_value": 0, "description": "强电电源故障"},
            "KZDY_GZ": {"type": "digital", "default_value": 0, "description": "控制电源故障"},
            "QDDY_DY_BJ": {"type": "digital", "default_value": 0, "description": "强电电源电压报警"},
            "KZDY_DY_BJ": {"type": "digital", "default_value": 0, "description": "控制电源电压报警"},
            "QDDC_TR_BJ": {"type": "digital", "default_value": 0, "description": "强电直流投入报警"},
            "KZDC_TR_BJ": {"type": "digital", "default_value": 0, "description": "控制直流投入报警"},
            "QDDC_DY_BJ": {"type": "digital", "default_value": 0, "description": "强电直流电压报警"},
            "KZDC_DY_BJ": {"type": "digital", "default_value": 0, "description": "控制直流电压报警"},
            "XDC_WD_GG_BJ": {"type": "digital", "default_value": 0, "description": "箱体温度过高报警"},
            "SX_QDDY_HL1_BJ": {"type": "digital", "default": [0, 1], "description": "上行强电电源回路1报警"},
            "SX_QDDY_HL2_BJ": {"type": "digital", "default": [0, 1], "description": "上行强电电源回路2报警"},
            "SX_QDDY_HL3_BJ": {"type": "digital", "default": [0, 1], "description": "上行强电电源回路3报警"},
            "SX_QDDY_HL4_BJ": {"type": "digital", "default": [0, 1], "description": "上行强电电源回路4报警"},
            "SX_QDDY_HL5_BJ": {"type": "digital", "default": [0, 1], "description": "上行强电电源回路5报警"},
            "SX_KZDY_SIG_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源信号回路报警"},
            "SX_KZDY_PSL_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源PSL回路报警"},
            "SX_KZDY_IBP_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源IBP回路报警"},
            "SX_KZDY_PEDC_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源PEDC回路报警"},
            "SX_KZDY_DCU_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源DCU回路报警"},
            "SX_KZDY_AQ_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源安全回路报警"},
            "SX_KZDY_JXTC_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源检修通道回路报警"},
            "SX_KZDY_DKD_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源端口端回路报警"},
            "SX_KZDY_LWD_HL_BJ": {"type": "digital", "default": [0, 1], "description": "上行控制电源列位端回路报警"},
            "XX_QDDY_HL1_BJ": {"type": "digital", "default": [0, 1], "description": "下行强电电源回路1报警"},
            "XX_QDDY_HL2_BJ": {"type": "digital", "default": [0, 1], "description": "下行强电电源回路2报警"},
            "XX_QDDY_HL3_BJ": {"type": "digital", "default": [0, 1], "description": "下行强电电源回路3报警"},
            "XX_QDDY_HL4_BJ": {"type": "digital", "default": [0, 1], "description": "下行强电电源回路4报警"},
            "XX_QDDY_HL5_BJ": {"type": "digital", "default": [0, 1], "description": "下行强电电源回路5报警"},
            "XX_KZDY_SIG_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源信号回路报警"},
            "XX_KZDY_PSL_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源PSL回路报警"},
            "XX_KZDY_IBP_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源IBP回路报警"},
            "XX_KZDY_PEDC_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源PEDC回路报警"},
            "XX_KZDY_DCU_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源DCU回路报警"},
            "XX_KZDY_AQ_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源安全回路报警"},
            "XX_KZDY_JXTC_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源检修通道回路报警"},
            "XX_KZDY_DKD_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源端口端回路报警"},
            "XX_KZDY_LWD_HL_BJ": {"type": "digital", "default": [0, 1], "description": "下行控制电源列位端回路报警"}
        }
    },
    "PS": {
        "topic_suffix": "PS",
        "description": "传感器数据",
        "fields": {
            "XDC_BM_WD": {"type": "analog", "default": [0, 100], "description": "箱体表面温度"},
            "XDC_NZ": {"type": "analog", "default": [0, 100], "description": "箱体内阻"},
            "XDC_DY": {"type": "analog", "default": [0, 100], "description": "箱体电压"},
            "QDDY_SC_DL": {"type": "analog", "default": [0, 100], "description": "强电电源输出电流"},
            "KZDY_SC_DL": {"type": "analog", "default": [0, 100], "description": "控制电源输出电流"},
            "QDDY_SC_DY": {"type": "analog", "default": [0, 100], "description": "强电电源输出电压"},
            "KZDY_SC_DY": {"type": "analog", "default": [0, 100], "description": "控制电源输出电压"}
        }
    },
    "ASD": {
        "topic_suffix": "ASD",
        "description": "自动滑动门数据",
        "has_direction": True,
        "has_index": True,
        "fields": {
            "LCB_GL": {"type": "digital", "default": [0, 1], "description": "列车边关联"},
            "LCB_ZD": {"type": "digital", "default": [0, 1], "description": "列车边制动"},
            "LCB_JDKM": {"type": "digital", "default": [0, 1], "description": "列车边紧急开门"},
            "LCB_JDGM": {"type": "digital", "default": [0, 1], "description": "列车边紧急关门"},
            "HDM_ZKDW": {"type": "digital", "default": [0, 1], "description": "滑动门正开到位"},
            "HDM_YKDW": {"type": "digital", "default": [0, 1], "description": "滑动门预开到位"},
            "HDM_ZGDW": {"type": "digital", "default": [0, 1], "description": "滑动门正关到位"},
            "HDM_YGDW": {"type": "digital", "default": [0, 1], "description": "滑动门正开到位"},
            "YC_KM": {"type": "digital", "default": [0, 1], "description": "遥控开门"},
            "YC_GM": {"type": "digital", "default": [0, 1], "description": "遥控关门"},
            "DWGL_ZT": {"type": "digital", "default": [0, 1], "description": "到位隔离状态"},
            "SDJS_BJ": {"type": "digital", "default": [0, 1], "description": "手动紧急报警"},
            "ZAWTC_BJ": {"type": "digital", "default": [0, 1], "description": "障碍物探测报警"},
            "DCSJSYC_BJ": {"type": "digital", "default": [0, 1], "description": "电磁锁紧急遥控报警"},
            "DCU_GZ": {"type": "digital", "default": [0, 1], "description": "DCU故障"},
            "ZXWKG_GZ": {"type": "digital", "default": [0, 1], "description": "正向无开关故障"},
            "YXWKG_GZ": {"type": "digital", "default": [0, 1], "description": "逆向无开关故障"},
            "DJ_GZ": {"type": "digital", "default": [0, 1], "description": "电机故障"},
            "GM_GZ": {"type": "digital", "default": [0, 1], "description": "关门故障"},
            "KM_GZ": {"type": "digital", "default": [0, 1], "description": "开门故障"},
            "DJ_DL": {"type": "analog", "default": [0, 100], "description": "电机电流"},
            "DJ_SD": {"type": "analog", "default": [0, 100], "description": "电机速度"},
            "DJ_SCNJ": {"type": "analog", "default": [0, 100], "description": "电机输出扭矩"},
            "QDB_DY": {"type": "analog", "default": [0, 100], "description": "驱动板电压"},
            "YC_KM_ML_DY": {"type": "analog", "default": [0, 100], "description": "遥控开门脉冲电压"},
            "YC_GM_ML_DY": {"type": "analog", "default": [0, 100], "description": "遥控关门脉冲电压"}
        }
    },
    "EED": {
        "topic_suffix": "EED",
        "description": "紧急疏散门数据",
        "has_direction": True,
        "has_index": True,
        "fields": {
            "YJM_DK": {"type": "digital", "default_value": 0, "description": "应急门打开"},
            "YJM_GB": {"type": "digital", "default_value": 0, "description": "应急门关闭"},
            "YJM_PL": {"type": "digital", "default_value": 0, "description": "应急门屏蔽"}
        }
    },
    "MSD": {
        "topic_suffix": "MSD",
        "description": "手动滑动门数据",
        "has_direction": True,
        "has_index": True,
        "fields": {
            "CT_DM_DK": {"type": "digital", "default_value": 0, "description": "车厅端门打开"},
            "CT_DM_GB": {"type": "digital", "default_value": 0, "description": "车厅端门关闭"},
            "CT_DM_DK_CS_BJ": {"type": "digital", "default_value": 0, "description": "车厅端门打开超时报警"},
            "CT_DM_PL": {"type": "digital", "default_value": 0, "description": "车厅端门屏蔽"},
            "CW_DM_DK": {"type": "digital", "default_value": 0, "description": "车外端门打开"},
            "CW_DM_GB": {"type": "digital", "default_value": 0, "description": "车外端门关闭"},
            "CW_DM_DK_CS_BJ": {"type": "digital", "default_value": 0, "description": "车外端门打开超时报警"},
            "CW_DM_PL": {"type": "digital", "default_value": 0, "description": "车外端门屏蔽"}
        }
    },
    "IBP": {
        "topic_suffix": "IBP",
        "description": "综合后备盘数据",
        "has_direction": True,
        "has_index": False,
        "fields": {
            "IBP_SNXX": {"type": "digital", "default": [0, 1], "description": "IBP上下行信息"},
            "IBP_SQ_PSL": {"type": "digital", "default": [0, 1], "description": "IBP授权PSL"},
            "IBP_KM_ML": {"type": "digital", "default": [0, 1], "description": "IBP开门脉冲"},
            "IBP_GM_ML": {"type": "digital", "default": [0, 1], "description": "IBP关门脉冲"}
        }
    },
    "PDS": {
        "topic_suffix": "PDS",
        "description": "站台门检测数据",
        "has_direction": True,
        "has_index": True,
        "fields": {
            "JXTC_BJ": {"type": "digital", "default": [0, 1], "description": "检修通道报警"},
            "JXTC_PL": {"type": "digital", "default": [0, 1], "description": "检修通道屏蔽"},
            "JXTC_GZ": {"type": "digital", "default": [0, 1], "description": "检修通道故障"}
        }
    },
    "PEDC": {
        "topic_suffix": "PEDC",
        "description": "站台门控制数据",
        "has_direction": True,
        "has_index": False,
        "fields": {
            "JKXT_PEDC_GZ": {"type": "digital", "default": [0, 1], "description": "监控系统PEDC故障"},
            "XCZX_GZ": {"type": "digital", "default": [0, 1], "description": "现场中心故障"},
            "ZC_HDM_GMSJ_XH_DY": {"type": "analog", "default": [0, 100], "description": "站台滑动门关门时间信号电压"},
            "ZC_YJM_GBSJ_XH_DY": {"type": "analog", "default": [0, 100], "description": "站台应急门关闭时间信号电压"},
            "ZC_JXTC_XT_AQ_XH_DY": {"type": "analog", "default": [0, 100], "description": "站台检修通道系统安全信号电压"},
            "KM_ML_SCJDQ_JC": {"type": "analog", "default": [0, 100], "description": "开门脉冲输出继电器检测"},
            "GM_ML_SCJDQ_JC": {"type": "analog", "default": [0, 100], "description": "关门脉冲输出继电器检测"}
        }
    },
    "PSL": {
        "topic_suffix": "PSL",
        "description": "站台安全线数据",
        "has_direction": True,
        "has_index": True,
        "fields": {
            "PSL_SN": {"type": "digital", "default": [0, 1], "description": "PSL上下行"},
            "PSL_KM": {"type": "digital", "default": [0, 1], "description": "PSL开门"},
            "PSL_GM": {"type": "digital", "default": [0, 1], "description": "PSL关门"},
            "PSL_HSJC": {"type": "digital", "default": [0, 1], "description": "PSL回送检测"}
        }
    },
    "SIG": {
        "topic_suffix": "SIG",
        "description": "信号数据",
        "has_direction": True,
        "has_index": False,
        "fields": {
            "XH_KM_ML": {"type": "digital", "default_value": 0, "description": "信号开门脉冲"},
            "XH_GM_ML": {"type": "digital", "default_value": 0, "description": "信号关门脉冲"},
            "ZW_XH": {"type": "digital", "default_value": 0, "description": "站位信号"},
            "HS_JC": {"type": "digital", "default_value": 0, "description": "回送检测"},
            "GB_SJ": {"type": "digital", "default_value": 0, "description": "关闭时间"},
            "PSD_GD_DY": {"type": "analog", "default": [58, 58], "description": "PSD供电电压"},
            "ZW_XH_DY": {"type": "analog", "default": [58, 58], "description": "站位信号电压"},
            "KM_ML_DY": {"type": "analog", "default": [58, 58], "description": "开门脉冲电压"},
            "GM_ML_DY": {"type": "analog", "default": [58, 58], "description": "关门脉冲电压"},
            "SIG_GD_DY": {"type": "analog", "default": [58, 58], "description": "信号供电电压"},
            "ZCGB_SJXH_DY": {"type": "analog", "default": [58, 58], "description": "站场关闭时间信号电压"},
            "HSJCXH_DY": {"type": "analog", "default": [58, 58], "description": "回送检测信号电压"}
        }
    }
}
