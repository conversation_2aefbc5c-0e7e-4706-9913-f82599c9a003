"""
开关量字段生成器
支持基于循环次数的开关量字段生成逻辑
"""
import random
from typing import Dict, Any


class DigitalFieldGenerator:
    """开关量字段生成器"""
    
    def __init__(self):
        self.field_states = {}  # 存储每个字段的状态
    
    def generate_digital_value(self, field_key: str, field_config: Dict, current_loop: int, total_loops: int) -> int:
        """
        生成开关量字段值
        
        Args:
            field_key: 字段唯一标识（包含车站ID和字段名）
            field_config: 字段配置
            current_loop: 当前循环次数（从1开始）
            total_loops: 总循环次数
            
        Returns:
            0或1
        """
        # 如果字段被禁用，返回0
        if not field_config.get('enabled', True):
            return 0
        
        # 获取默认值
        default_value = field_config.get('default_value', 0)
        
        # 获取触发循环次数
        trigger_loop = field_config.get('trigger_loop', 0)
        
        # 获取触发模式
        trigger_mode = field_config.get('trigger_mode', 'once')  # once, toggle, random
        
        # 如果设置了固定值，直接返回
        fixed_value = field_config.get('fixed_value', '')
        if fixed_value != '':
            try:
                return int(fixed_value)
            except ValueError:
                pass
        
        # 如果触发循环次数为0或大于总循环次数，返回默认值
        if trigger_loop <= 0 or trigger_loop > total_loops:
            return default_value
        
        # 根据触发模式处理
        if trigger_mode == 'once':
            # 只在指定循环触发一次
            if current_loop == trigger_loop:
                return 1 - default_value  # 切换到相反值
            else:
                return default_value
                
        elif trigger_mode == 'toggle':
            # 每隔trigger_loop个循环切换一次状态
            if trigger_loop <= 0:
                return default_value

            # 计算当前处于第几个周期
            cycle_position = (current_loop - 1) % (trigger_loop * 2)

            # 在前trigger_loop个循环中返回默认值，后trigger_loop个循环中返回相反值
            if cycle_position < trigger_loop:
                return default_value
            else:
                return 1 - default_value
                
        elif trigger_mode == 'random':
            # 从指定循环开始随机生成
            if current_loop >= trigger_loop:
                return random.randint(0, 1)
            else:
                return default_value
                
        elif trigger_mode == '持续':
            # 从指定循环开始持续为1
            if current_loop >= trigger_loop:
                return 1
            else:
                return default_value
        
        # 默认返回默认值
        return default_value
    
    def reset_field_states(self):
        """重置所有字段状态"""
        self.field_states.clear()
    
    def get_trigger_modes(self):
        """获取支持的触发模式"""
        return {
            'once': '单次触发（仅在指定循环触发一次）',
            'toggle': '切换模式（从指定循环开始每次切换）',
            'random': '随机模式（从指定循环开始随机0/1）',
            '持续': '持续模式（从指定循环开始持续为1）'
        }


# 全局实例
digital_generator = DigitalFieldGenerator()
