"""
数据生成器模块
支持基于配置的多车站数据生成，支持详细的字段级配置
"""
import json
import random
import time
from typing import Dict, List, Any, Tuple
from data_fields_config import DATA_FIELDS_CONFIG
from digital_field_generator import digital_generator


class DataTemplate:
    """数据模板基类"""

    def __init__(self, template_name: str):
        self.template_name = template_name

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成数据的抽象方法"""
        raise NotImplementedError

    def generate_field_value(self, field_name: str, field_config: Dict, station_field_config: Dict = None,
                           station_id: str = "", current_loop: int = 1, total_loops: int = 1) -> Any:
        """根据配置生成字段值"""
        # 获取车站特定的字段配置
        if station_field_config and field_name in station_field_config:
            config = station_field_config[field_name]
        else:
            # 使用默认配置
            if field_config['type'] == 'digital':
                config = {
                    'default_value': field_config.get('default_value', 0),
                    'trigger_loop': 0,
                    'trigger_mode': 'once',
                    'enabled': True,
                    'fixed_value': ''
                }
            else:
                config = {
                    'range': field_config.get('default', [0, 100]),
                    'enabled': True,
                    'fixed_value': ''
                }

        # 如果字段被禁用，返回0
        if not config.get('enabled', True):
            return 0

        # 如果设置了固定值，返回固定值
        fixed_value = config.get('fixed_value', '').strip()
        if fixed_value:
            try:
                return int(fixed_value)
            except ValueError:
                pass

        # 根据字段类型生成值
        if field_config['type'] == 'digital':
            # 开关量字段
            field_key = f"{station_id}_{field_name}"
            return digital_generator.generate_digital_value(field_key, config, current_loop, total_loops)
        else:
            # 数字量字段
            range_config = config.get('range', field_config.get('default', [0, 100]))
            return random.randint(range_config[0], range_config[1])


class PowerDataTemplate(DataTemplate):
    """电源数据模板"""

    def __init__(self):
        super().__init__("POWER")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成电源数据"""
        # 获取字段配置
        field_configs = station_config.get("field_configs", {}).get("POWER", {})
        power_fields = DATA_FIELDS_CONFIG["POWER"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}

        # 为每个字段生成值
        for field_name, field_config in power_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )

        return data


class PSDataTemplate(DataTemplate):
    """PS数据模板"""

    def __init__(self):
        super().__init__("PS")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成PS数据"""
        # 获取字段配置
        field_configs = station_config.get("field_configs", {}).get("PS", {})
        ps_fields = DATA_FIELDS_CONFIG["PS"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}

        # 为每个字段生成值
        for field_name, field_config in ps_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )

        return data


class ASDDataTemplate(DataTemplate):
    """ASD数据模板"""

    def __init__(self):
        super().__init__("ASD")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成ASD数据"""
        field_configs = station_config.get("field_configs", {}).get("ASD", {})
        asd_fields = DATA_FIELDS_CONFIG["ASD"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in asd_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class EEDDataTemplate(DataTemplate):
    """EED数据模板"""

    def __init__(self):
        super().__init__("EED")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成EED数据"""
        field_configs = station_config.get("field_configs", {}).get("EED", {})
        eed_fields = DATA_FIELDS_CONFIG["EED"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in eed_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class MSDDataTemplate(DataTemplate):
    """MSD数据模板"""

    def __init__(self):
        super().__init__("MSD")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成MSD数据"""
        field_configs = station_config.get("field_configs", {}).get("MSD", {})
        msd_fields = DATA_FIELDS_CONFIG["MSD"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in msd_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class IBPDataTemplate(DataTemplate):
    """IBP数据模板"""

    def __init__(self):
        super().__init__("IBP")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成IBP数据"""
        field_configs = station_config.get("field_configs", {}).get("IBP", {})
        ibp_fields = DATA_FIELDS_CONFIG["IBP"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in ibp_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class PDSDataTemplate(DataTemplate):
    """PDS数据模板"""

    def __init__(self):
        super().__init__("PDS")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成PDS数据"""
        field_configs = station_config.get("field_configs", {}).get("PDS", {})
        pds_fields = DATA_FIELDS_CONFIG["PDS"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in pds_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class PEDCDataTemplate(DataTemplate):
    """PEDC数据模板"""

    def __init__(self):
        super().__init__("PEDC")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成PEDC数据"""
        field_configs = station_config.get("field_configs", {}).get("PEDC", {})
        pedc_fields = DATA_FIELDS_CONFIG["PEDC"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in pedc_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class PSLDataTemplate(DataTemplate):
    """PSL数据模板"""

    def __init__(self):
        super().__init__("PSL")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成PSL数据"""
        field_configs = station_config.get("field_configs", {}).get("PSL", {})
        psl_fields = DATA_FIELDS_CONFIG["PSL"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in psl_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class SIGDataTemplate(DataTemplate):
    """SIG数据模板"""

    def __init__(self):
        super().__init__("SIG")

    def generate(self, station_config: Dict, timestamp: int, current_loop: int = 1, total_loops: int = 1) -> Dict:
        """生成SIG数据"""
        field_configs = station_config.get("field_configs", {}).get("SIG", {})
        sig_fields = DATA_FIELDS_CONFIG["SIG"]["fields"]
        station_id = station_config.get("station_id", "")

        data = {"timestamp": timestamp}
        for field_name, field_config in sig_fields.items():
            data[field_name] = self.generate_field_value(
                field_name, field_config, field_configs, station_id, current_loop, total_loops
            )
        return data


class StationDataGenerator:
    """车站数据生成器"""

    def __init__(self):
        self.templates = {
            "POWER": PowerDataTemplate(),
            "PS": PSDataTemplate(),
            "ASD": ASDDataTemplate(),
            "EED": EEDDataTemplate(),
            "MSD": MSDDataTemplate(),
            "IBP": IBPDataTemplate(),
            "PDS": PDSDataTemplate(),
            "PEDC": PEDCDataTemplate(),
            "PSL": PSLDataTemplate(),
            "SIG": SIGDataTemplate()
        }
    
    def generate_station_data(self, line_id: str, station_config: Dict, current_loop: int = 1, total_loops: int = 1) -> List[Dict]:
        """为单个车站生成所有数据"""
        station_id = station_config["station_id"]
        config = station_config["config"]
        timestamp = int(time.time() * 1000)
        messages = []

        # 生成电源数据
        power_data = self.templates["POWER"].generate(station_config, timestamp, current_loop, total_loops)
        messages.append({
            "topic": f"{line_id}/{station_id}/PSD/POWER",
            "payload": power_data
        })

        # 生成PS数据
        ps_data = self.templates["PS"].generate(station_config, timestamp, current_loop, total_loops)
        messages.append({
            "topic": f"{line_id}/{station_id}/PSD/PS",
            "payload": ps_data
        })
        
        # 生成ASD数据
        for direction in ["SX", "XX"]:
            asd_count = config["asd_count"][direction.lower()]
            for i in range(1, asd_count + 1):
                asd_data = self.templates["ASD"].generate(station_config, timestamp, current_loop, total_loops)
                messages.append({
                    "topic": f"{line_id}/{station_id}/PSD/{direction}/ASD{i:02d}",
                    "payload": asd_data
                })

        # 生成EED数据
        for direction in ["SX", "XX"]:
            eed_count = config["eed_count"][direction.lower()]
            for i in range(1, eed_count + 1):
                eed_data = self.templates["EED"].generate(station_config, timestamp, current_loop, total_loops)
                messages.append({
                    "topic": f"{line_id}/{station_id}/PSD/{direction}/EED{i:02d}",
                    "payload": eed_data
                })

        # 生成MSD数据
        for direction in ["SX", "XX"]:
            msd_count = config["msd_count"][direction.lower()]
            for i in range(1, msd_count + 1):
                msd_data = self.templates["MSD"].generate(station_config, timestamp, current_loop, total_loops)
                messages.append({
                    "topic": f"{line_id}/{station_id}/PSD/{direction}/MSD{i:02d}",
                    "payload": msd_data
                })

        # 生成IBP数据
        for direction in ["SX", "XX"]:
            ibp_data = self.templates["IBP"].generate(station_config, timestamp, current_loop, total_loops)
            messages.append({
                "topic": f"{line_id}/{station_id}/PSD/{direction}/IBP",
                "payload": ibp_data
            })

        # 生成PDS数据
        for direction in ["SX", "XX"]:
            pds_count = config["pds_count"][direction.lower()]
            for i in range(1, pds_count + 1):
                pds_data = self.templates["PDS"].generate(station_config, timestamp, current_loop, total_loops)
                messages.append({
                    "topic": f"{line_id}/{station_id}/PSD/{direction}/PDS{i:02d}",
                    "payload": pds_data
                })

        # 生成PEDC数据
        for direction in ["SX", "XX"]:
            pedc_data = self.templates["PEDC"].generate(station_config, timestamp, current_loop, total_loops)
            messages.append({
                "topic": f"{line_id}/{station_id}/PSD/{direction}/PEDC",
                "payload": pedc_data
            })

        # 生成PSL数据
        for direction in ["SX", "XX"]:
            psl_count = config["psl_count"][direction.lower()]
            for i in range(1, psl_count + 1):
                psl_data = self.templates["PSL"].generate(station_config, timestamp, current_loop, total_loops)
                messages.append({
                    "topic": f"{line_id}/{station_id}/PSD/{direction}/PSL{i:02d}",
                    "payload": psl_data
                })

        # 生成SIG数据
        for direction in ["SX", "XX"]:
            sig_data = self.templates["SIG"].generate(station_config, timestamp, current_loop, total_loops)
            messages.append({
                "topic": f"{line_id}/{station_id}/PSD/{direction}/SIG",
                "payload": sig_data
            })

        return messages

    def generate_multiple_stations_data(self, line_id: str, stations_config: List[Dict]) -> Dict[str, List[Dict]]:
        """为多个车站生成数据"""
        all_messages = {}
        for station_config in stations_config:
            if station_config.get("enabled", True):
                station_id = station_config["station_id"]
                messages = self.generate_station_data(line_id, station_config)
                all_messages[station_id] = messages
        return all_messages
