"""
配置文件管理器
支持配置文件的保存、加载、导入、导出、验证等功能
"""
import json
import os
from typing import Dict, List, Any, Optional
import logging


class ConfigManager:
    """配置文件管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def load_config(self, filename: str) -> Optional[Dict]:
        """加载配置文件"""
        try:
            if not os.path.exists(filename):
                self.logger.warning(f"配置文件不存在: {filename}")
                return None
            
            with open(filename, "r", encoding="utf-8") as f:
                config = json.load(f)
            
            # 验证配置文件格式
            if self.validate_config(config):
                self.logger.info(f"成功加载配置文件: {filename}")
                return config
            else:
                self.logger.error(f"配置文件格式无效: {filename}")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件JSON格式错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return None
    
    def save_config(self, config: Dict, filename: str) -> bool:
        """保存配置文件"""
        try:
            # 验证配置
            if not self.validate_config(config):
                self.logger.error("配置数据无效，无法保存")
                return False
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else ".", exist_ok=True)
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置文件已保存: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def validate_config(self, config: Dict) -> bool:
        """验证配置文件格式"""
        try:
            # 检查必需的顶级字段
            required_fields = ["line_info", "mqtt_config", "global_settings", "stations"]
            for field in required_fields:
                if field not in config:
                    self.logger.error(f"缺少必需字段: {field}")
                    return False
            
            # 验证线路信息
            line_info = config["line_info"]
            if not isinstance(line_info, dict) or "line_id" not in line_info:
                self.logger.error("线路信息格式无效")
                return False
            
            # 验证MQTT配置
            mqtt_config = config["mqtt_config"]
            if not isinstance(mqtt_config, dict) or "server" not in mqtt_config or "port" not in mqtt_config:
                self.logger.error("MQTT配置格式无效")
                return False
            
            # 验证全局设置
            global_settings = config["global_settings"]
            if not isinstance(global_settings, dict):
                self.logger.error("全局设置格式无效")
                return False
            
            # 验证车站配置
            stations = config["stations"]
            if not isinstance(stations, list):
                self.logger.error("车站配置必须是列表")
                return False
            
            for i, station in enumerate(stations):
                if not self.validate_station_config(station):
                    self.logger.error(f"车站配置 {i} 格式无效")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def validate_station_config(self, station: Dict) -> bool:
        """验证单个车站配置"""
        try:
            # 检查必需字段
            required_fields = ["station_id", "station_name", "enabled", "config"]
            for field in required_fields:
                if field not in station:
                    return False
            
            # 验证设备配置
            config = station["config"]
            required_device_types = ["asd_count", "eed_count", "msd_count", "pds_count", "psl_count"]
            for device_type in required_device_types:
                if device_type not in config:
                    return False
                
                device_config = config[device_type]
                if not isinstance(device_config, dict) or "sx" not in device_config or "xx" not in device_config:
                    return False
                
                # 检查数值是否为正整数
                if not isinstance(device_config["sx"], int) or device_config["sx"] < 0:
                    return False
                if not isinstance(device_config["xx"], int) or device_config["xx"] < 0:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def create_default_config(self) -> Dict:
        """创建默认配置"""
        return {
            "line_info": {
                "line_id": "L03",
                "line_name": "3号线",
                "description": "地铁3号线站台门模拟数据"
            },
            "mqtt_config": {
                "server": "*************",
                "port": 1883,
                "username": "",
                "password": "",
                "qos": 0,
                "retain": False
            },
            "global_settings": {
                "send_interval": 1000,
                "loop_count": 1,
                "concurrent_stations": 5,
                "data_variation": 0.1
            },
            "stations": []
        }
    
    def create_default_station_config(self, station_id: str, station_name: str) -> Dict:
        """创建默认车站配置"""
        return {
            "station_id": station_id,
            "station_name": station_name,
            "enabled": True,
            "config": {
                "asd_count": {"sx": 30, "xx": 30},
                "eed_count": {"sx": 8, "xx": 8},
                "msd_count": {"sx": 2, "xx": 2},
                "pds_count": {"sx": 8, "xx": 8},
                "psl_count": {"sx": 3, "xx": 3}
            },
            "data_ranges": {
                "power_data": {
                    "digital_signals": [0, 1],
                    "analog_signals": [0, 100]
                },
                "ps_data": {
                    "temperature": [0, 100],
                    "voltage": [0, 100],
                    "current": [0, 100]
                },
                "asd_data": {
                    "digital_signals": [0, 1],
                    "motor_current": [0, 100],
                    "motor_speed": [0, 100],
                    "motor_torque": [0, 100],
                    "door_voltage": [0, 100],
                    "control_voltage": [0, 100]
                },
                "eed_data": {
                    "status": [0, 1],
                    "temperature": [0, 100],
                    "voltage": [0, 100]
                },
                "msd_data": {
                    "status": [0, 1],
                    "pressure": [0, 100],
                    "flow": [0, 100]
                }
            }
        }
    
    def export_stations_to_csv(self, stations: List[Dict], filename: str) -> bool:
        """导出车站配置到CSV文件"""
        try:
            import csv
            
            with open(filename, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                
                # 写入标题行
                headers = [
                    "车站编号", "车站名称", "启用状态",
                    "ASD上行", "ASD下行", "EED上行", "EED下行",
                    "MSD上行", "MSD下行", "PDS上行", "PDS下行",
                    "PSL上行", "PSL下行"
                ]
                writer.writerow(headers)
                
                # 写入数据行
                for station in stations:
                    config = station["config"]
                    row = [
                        station["station_id"],
                        station["station_name"],
                        "是" if station["enabled"] else "否",
                        config["asd_count"]["sx"],
                        config["asd_count"]["xx"],
                        config["eed_count"]["sx"],
                        config["eed_count"]["xx"],
                        config["msd_count"]["sx"],
                        config["msd_count"]["xx"],
                        config["pds_count"]["sx"],
                        config["pds_count"]["xx"],
                        config["psl_count"]["sx"],
                        config["psl_count"]["xx"]
                    ]
                    writer.writerow(row)
            
            self.logger.info(f"车站配置已导出到CSV: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出CSV失败: {e}")
            return False
    
    def import_stations_from_csv(self, filename: str) -> Optional[List[Dict]]:
        """从CSV文件导入车站配置"""
        try:
            import csv
            
            stations = []
            with open(filename, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    station = self.create_default_station_config(
                        row["车站编号"],
                        row["车站名称"]
                    )
                    
                    station["enabled"] = row["启用状态"] == "是"
                    station["config"]["asd_count"]["sx"] = int(row["ASD上行"])
                    station["config"]["asd_count"]["xx"] = int(row["ASD下行"])
                    station["config"]["eed_count"]["sx"] = int(row["EED上行"])
                    station["config"]["eed_count"]["xx"] = int(row["EED下行"])
                    station["config"]["msd_count"]["sx"] = int(row["MSD上行"])
                    station["config"]["msd_count"]["xx"] = int(row["MSD下行"])
                    station["config"]["pds_count"]["sx"] = int(row["PDS上行"])
                    station["config"]["pds_count"]["xx"] = int(row["PDS下行"])
                    station["config"]["psl_count"]["sx"] = int(row["PSL上行"])
                    station["config"]["psl_count"]["xx"] = int(row["PSL下行"])
                    
                    stations.append(station)
            
            self.logger.info(f"从CSV导入了 {len(stations)} 个车站配置")
            return stations
            
        except Exception as e:
            self.logger.error(f"从CSV导入失败: {e}")
            return None
    
    def backup_config(self, config: Dict, backup_dir: str = "backups") -> bool:
        """备份配置文件"""
        try:
            import datetime
            
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = os.path.join(backup_dir, f"config_backup_{timestamp}.json")
            
            return self.save_config(config, backup_filename)
            
        except Exception as e:
            self.logger.error(f"备份配置失败: {e}")
            return False
